<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_1oY3zqIService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.1oY3zqI' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.1oY3zqI'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'userVideoAccessRepository' => ['privates', 'App\\Repository\\UserVideoAccessRepository', 'getUserVideoAccessRepositoryService', true],
            'videoRepository' => ['privates', 'App\\Repository\\VideoRepository', 'getVideoRepositoryService', true],
        ], [
            'entityManager' => '?',
            'userVideoAccessRepository' => 'App\\Repository\\UserVideoAccessRepository',
            'videoRepository' => 'App\\Repository\\VideoRepository',
        ]);
    }
}
