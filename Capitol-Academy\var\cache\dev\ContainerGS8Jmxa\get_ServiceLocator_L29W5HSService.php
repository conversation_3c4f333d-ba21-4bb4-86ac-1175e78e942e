<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_L29W5HSService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.L29W5HS' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.L29W5HS'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'instructorRepository' => ['privates', 'App\\Repository\\InstructorRepository', 'getInstructorRepositoryService', true],
        ], [
            'instructorRepository' => 'App\\Repository\\InstructorRepository',
        ]);
    }
}
