{% extends 'base.html.twig' %}

{% block title %}Courses - Capitol Academy{% endblock %}

{% block meta_description %}Explore our comprehensive trading courses at Capitol Academy. Learn financial markets, forex trading, cryptocurrency, and more with expert instructors.{% endblock %}

{% block stylesheets %}
<style>
.courses-hero {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    color: white;
    padding: 5rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.courses-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.courses-hero .container {
    position: relative;
    z-index: 2;
}

.courses-hero h1 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.courses-hero .lead {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.95;
}

.courses-section {
    padding: 5rem 0;
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: #011a2d;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-header p {
    color: #6c757d;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.course-card {
    background: white;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(1, 26, 45, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    height: 100%;
    border: 1px solid rgba(1, 26, 45, 0.06);
    position: relative;
    backdrop-filter: blur(10px);
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #011a2d 0%, #a90418 50%, #011a2d 100%);
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 24px 24px 0 0;
}

.course-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(1, 26, 45, 0.02) 0%, rgba(169, 4, 24, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 24px;
    pointer-events: none;
}

.course-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px rgba(1, 26, 45, 0.18), 0 0 0 1px rgba(1, 26, 45, 0.1);
}

.course-card:hover::before {
    opacity: 1;
    height: 6px;
}

.course-card:hover::after {
    opacity: 1;
}

.course-thumbnail {
    width: 100%;
    height: 240px;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.course-card:hover .course-thumbnail {
    transform: scale(1.08);
    filter: brightness(1.1) saturate(1.1);
}

.course-thumbnail-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(1, 26, 45, 0.1) 0%, rgba(169, 4, 24, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.course-card:hover .course-thumbnail-overlay {
    opacity: 1;
}

.course-content {
    padding: 2rem;
}

.course-title {
    color: #011a2d;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    gap: 1rem;
}

.course-meta span {
    display: flex;
    align-items: center;
    color: #6c757d;
    background: #f8f9fa;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
}

.course-meta i {
    color: #a90418;
    margin-right: 0.5rem;
}

.course-description {
    color: #495057;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.btn-course-contact {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    color: white;
    padding: 0.875rem 1.75rem;
    border-radius: 30px;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    box-shadow: 0 6px 20px rgba(1, 26, 45, 0.15);
    flex: 1;
    text-align: center;
    min-width: 160px;
    position: relative;
    overflow: hidden;
}

.btn-course-contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.btn-course-contact:hover::before {
    left: 100%;
}

.btn-course-contact:hover {
    background: linear-gradient(135deg, #a90418 0%, #d63447 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(169, 4, 24, 0.25);
}

.btn-course-view {
    background: transparent;
    color: #011a2d;
    border: 2px solid #011a2d;
    padding: 0.875rem 1.75rem;
    border-radius: 30px;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex: 1;
    text-align: center;
    min-width: 140px;
    position: relative;
    overflow: hidden;
}

.btn-course-view::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: #011a2d;
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-course-view:hover::before {
    width: 100%;
}

.btn-course-view:hover {
    color: white;
    text-decoration: none;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(1, 26, 45, 0.2);
    border-color: #011a2d;
}

.empty-state {
    text-align: center;
    padding: 6rem 2rem;
    color: #6c757d;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    margin: 2rem 0;
}

.empty-state i {
    font-size: 5rem;
    margin-bottom: 2rem;
    color: #dee2e6;
    background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.empty-state h3 {
    color: #011a2d;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.empty-state .btn {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    background: linear-gradient(135deg, #a90418 0%, #d63447 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(169, 4, 24, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .courses-hero {
        padding: 3rem 0;
    }

    .courses-hero h1 {
        font-size: 2.5rem;
    }

    .course-actions {
        flex-direction: column;
    }

    .btn-course-contact,
    .btn-course-view {
        flex: none;
        width: 100%;
    }

    .course-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .course-meta span {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .course-content {
        padding: 1.5rem;
    }

    .courses-section {
        padding: 3rem 0;
    }

    .section-header {
        margin-bottom: 3rem;
    }
}
</style>
{% endblock %}

{% block body %}
<!-- Hero Section -->
<section class="courses-hero">
    <div class="container">
        <h1 class="display-4 mb-3">Our Trading Courses</h1>
        <p class="lead">Master the financial markets with our comprehensive onsite trading courses designed by industry experts</p>
        <div class="mt-4">
            <span class="badge bg-light text-dark fs-6 px-3 py-2 me-3">
                <i class="fas fa-users me-2"></i>Expert Instructors
            </span>
            <span class="badge bg-light text-dark fs-6 px-3 py-2 me-3">
                <i class="fas fa-certificate me-2"></i>Professional Certification
            </span>
            <span class="badge bg-light text-dark fs-6 px-3 py-2">
                <i class="fas fa-handshake me-2"></i>Hands-on Learning
            </span>
        </div>
    </div>
</section>

<!-- Courses Section -->
<section class="courses-section">
    <div class="container">
        {% if courses|length > 0 %}
            <div class="section-header">
                <h2>Available Courses</h2>
                <p>Choose from our carefully crafted curriculum designed to take you from beginner to professional trader</p>
            </div>

            <div class="row g-4">
                {% for course in courses %}
                    <div class="col-lg-4 col-md-6">
                        <div class="course-card">
                            {% if course.thumbnailImage %}
                                <div style="overflow: hidden; border-radius: 24px 24px 0 0; position: relative;">
                                    <img src="{{ course.thumbnailUrl }}" alt="{{ course.title }}" class="course-thumbnail">
                                    <div class="course-thumbnail-overlay"></div>
                                </div>
                            {% else %}
                                <div class="course-thumbnail d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 24px 24px 0 0; position: relative;">
                                    <div class="text-center">
                                        <i class="fas fa-graduation-cap" style="font-size: 3.5rem; color: #011a2d; margin-bottom: 0.5rem; opacity: 0.7;"></i>
                                        <p class="mb-0 text-muted">Course Image</p>
                                    </div>
                                    <div class="course-thumbnail-overlay"></div>
                                </div>
                            {% endif %}

                            <div class="course-content">
                                <h5 class="course-title">{{ course.title }}</h5>

                                <div class="course-meta">
                                    <span><i class="fas fa-tag"></i>{{ course.category ?? 'General' }}</span>
                                    <span><i class="fas fa-signal"></i>{{ course.level ?? 'All Levels' }}</span>
                                </div>

                                <!-- Course Description -->
                                <p class="course-description">{{ course.description ?? 'Comprehensive training program designed to enhance your trading skills and market knowledge.' }}</p>

                                <!-- Course Actions -->
                                <div class="course-actions">
                                    <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" class="btn-course-contact">
                                        <i class="fas fa-envelope me-2"></i>Enroll Now
                                    </a>
                                    <a href="{{ path(course.routeName, {code: course.code}) }}" class="btn-course-view">
                                        <i class="fas fa-eye me-2"></i>Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-graduation-cap"></i>
                <h3>No Courses Available</h3>
                <p>We're currently preparing our comprehensive course offerings. Our expert instructors are developing cutting-edge curriculum to help you master the financial markets.</p>
                <a href="{{ path('app_contact') }}" class="btn btn-primary">
                    <i class="fas fa-envelope me-2"></i>Get Notified When Available
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
