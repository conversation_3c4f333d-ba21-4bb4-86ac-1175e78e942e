<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* home/index.html.twig */
class __TwigTemplate_f7c7ec466eee9e403b41d6c8bc23f870 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "home/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "home/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Capitol Academy - Professional Financial Training";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "
<main class=\"homepage-main\">
    <!-- Section 1: Hero Section -->
    <section class=\"hero-section position-relative overflow-hidden\" style=\"height: 80vh;\">
        <!-- Background Image -->
        <div class=\"hero-background\" style=\"
            background-image: url('";
        // line 12
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Banner 1 HP.jpeg"), "html", null, true);
        yield "');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        \"></div>

        <!-- Light Overlay for Text Readability -->
        <div class=\"hero-overlay\" style=\"
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(1, 26, 45, 0.2);
            z-index: 2;
        \"></div>

        <!-- Hero Content -->
        <div class=\"container position-relative\" style=\"z-index: 3; height: 80vh;\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-8\">
                    <div class=\"hero-content text-white py-4\">
                        <h1 class=\"hero-title fw-bold mb-4\" style=\"
                            font-size: 3.5rem;
                            line-height: 1.2;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                            color: white;
                            font-family: 'Montserrat', sans-serif;
                        \">
                            <div class=\"mb-2\">You Can LEARN.</div>
                            <div class=\"mb-2\">We Can TEACH.</div>
                            <div class=\"mb-2\">We'll Be Great.</div>
                            <div>TOGETHER.</div>
                        </h1>

                        <div class=\"hero-actions\">
                            <a href=\"";
        // line 54
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_registration");
        yield "\" class=\"btn btn-outline-light px-3 py-3\" style=\"
                                border: 2px solid white;
                                background: transparent;
                                color: white;
                                font-weight: 600;
                                border-radius: 8px;
                                transition: all 0.3s ease;
                                font-size: 1.1rem;
                            \" onmouseover=\"this.style.background='white'; this.style.color='#011a2d'; this.style.borderColor='white'; this.style.borderRadius='8px';\"
                               onmouseout=\"this.style.background='transparent'; this.style.color='white'; this.style.borderColor='white'; this.style.borderRadius='8px';\">
                                <i class=\"fas fa-user-plus me-2\"></i>Register Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 2: Trusted Partners -->
    <section class=\"partners-section py-4\" style=\"background: white; margin: 0; padding-top: 2rem; padding-bottom: 2rem;\">
        <div class=\"container position-relative\">
            <!-- Inline Trusted by text with logos -->
            <div class=\"d-flex align-items-center justify-content-center\">
                <h3 class=\"fw-bold text-dark me-5 mb-2\" style=\"font-size: 1.5rem; white-space: nowrap; margin-right: 3rem !important;\">Trusted by</h3>

                <!-- Enhanced Seamless Scrolling Partners Logos -->
                <div class=\"partners-carousel-container overflow-hidden position-relative flex-grow-1\" style=\"height: 50px;\">
                    <div class=\"partners-carousel\" id=\"partnersCarousel\">
                        ";
        // line 83
        $context["partners"] = $this->extensions['App\Twig\PartnerExtension']->getActivePartners();
        // line 84
        yield "                        ";
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["partners"]) || array_key_exists("partners", $context) ? $context["partners"] : (function () { throw new RuntimeError('Variable "partners" does not exist.', 84, $this->source); })())) > 0)) {
            // line 85
            yield "                            <!-- First complete set of logos -->
                            <div class=\"partner-set d-flex align-items-center\">
                                ";
            // line 87
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["partners"]) || array_key_exists("partners", $context) ? $context["partners"] : (function () { throw new RuntimeError('Variable "partners" does not exist.', 87, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["partner"]) {
                // line 88
                yield "                                <div class=\"partner-logo flex-shrink-0 mx-4 d-flex align-items-center justify-content-center\" style=\"min-width: 80px; height: 50px;\">
                                    <img src=\"";
                // line 89
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["partner"], "logoUrl", [], "any", false, false, false, 89), "html", null, true);
                yield "\" alt=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["partner"], "name", [], "any", false, false, false, 89), "html", null, true);
                yield "\"
                                         class=\"img-fluid partner-img\"
                                         style=\"
                                             height: 35px;
                                             opacity: 1;
                                             transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                             object-fit: contain;
                                             filter: grayscale(0%) brightness(1);
                                         \"
                                         onmouseover=\"this.style.transform='scale(1.1)'\"
                                         onmouseout=\"this.style.transform='scale(1)'\"
                                         onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                                </div>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['partner'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 103
            yield "                            </div>

                            <!-- Exact duplicate set for seamless infinite loop -->
                            <div class=\"partner-set d-flex align-items-center\">
                                ";
            // line 107
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["partners"]) || array_key_exists("partners", $context) ? $context["partners"] : (function () { throw new RuntimeError('Variable "partners" does not exist.', 107, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["partner"]) {
                // line 108
                yield "                                <div class=\"partner-logo flex-shrink-0 mx-4 d-flex align-items-center justify-content-center\" style=\"min-width: 80px; height: 50px;\">
                                    <img src=\"";
                // line 109
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["partner"], "logoUrl", [], "any", false, false, false, 109), "html", null, true);
                yield "\" alt=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["partner"], "name", [], "any", false, false, false, 109), "html", null, true);
                yield "\"
                                         class=\"img-fluid partner-img\"
                                         style=\"
                                             height: 35px;
                                             opacity: 1;
                                             transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                             object-fit: contain;
                                             filter: grayscale(0%) brightness(1);
                                         \"
                                         onmouseover=\"this.style.transform='scale(1.1)'\"
                                         onmouseout=\"this.style.transform='scale(1)'\"
                                         onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                                </div>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['partner'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 123
            yield "                            </div>
                        ";
        } else {
            // line 125
            yield "                            <!-- Fallback if no partners in database -->
                            <div class=\"text-center w-100 d-flex align-items-center justify-content-center\" style=\"height: 50px;\">
                                <p class=\"text-muted mb-0\">Partner logos will appear here</p>
                            </div>
                        ";
        }
        // line 130
        yield "                    </div>
                </div>
            </div>



        </div>
    </section>

    <!-- Section 3: Our Courses -->
    <section class=\"courses-section position-relative\" style=\"
        background-image: url('";
        // line 141
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/fond-sitemap.png"), "html", null, true);
        yield "');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        margin-top: 2rem;
        padding: 4rem 0;
    \">
        <!-- Enhanced Darker Overlay for Better Contrast -->
        <div class=\"position-absolute top-0 start-0 w-100 h-100\" style=\"background-image: url('";
        // line 150
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/fond-sitemap.png"), "html", null, true);
        yield "'); border: 10px solid rgba(255, 255, 255, 0.1); z-index: 1;\"></div>

        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <!-- Enhanced Main Section Title -->
            <div class=\"row mb-5\">
                <div class=\"col-12 text-center\">
                    <h2 class=\"fw-bold mb-4\" style=\"
                        font-size: 2.5rem;
                        color: #00233e;
                        margin-bottom: 2rem;
                        font-family: 'Montserrat', sans-serif;
                        font-weight: 700;
                        line-height: 1.1;
                    \">Our Courses</h2>
                </div>
            </div>

            <!-- Content Area -->
            <div class=\"row mb-5 align-items-center\">
                <!-- Left: Text Content -->
                <div class=\"col-lg-7 pe-5 d-flex flex-column justify-content-center\">
                    <h3 class=\"fw-bold mb-4\" style=\"
                        font-size: 1.3rem;
                        color: #971020;
                        font-family: 'Montserrat', sans-serif;
                        font-weight: 600;
                        line-height: 1.2;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    \">Financial Market Technicals Analysis Certified Courses</h3>

                    <p class=\"mb-4\" style=\"
                        font-size: 1.4rem;
                        line-height: 1.6;
                        color: #45403f;
                        font-family: 'Calibri', Arial, sans-serif;
                        font-weight: 400;
                        margin-right: 3rem;
                    \">
                        From finance fundamentals to advanced deep-dives, each course offers video lessons, practical templates,
                        interactive exercises, skill validation, and a certificate of completion. Over 75% of Capitol Academy
                        learners report improved productivity or competency within weeks.
                    </p>
                </div>

                <!-- Right: Certificate Image -->
                <div class=\"col-lg-5 d-flex align-items-center justify-content-center ps-4\">
                    <div class=\"certificate-container\" style=\"
                        transition: all 0.3s ease;
                        transform-style: preserve-3d;
                    \" onmouseover=\"this.style.transform='rotateY(5deg) rotateX(5deg) scale(1.05)'\"
                       onmouseout=\"this.style.transform='rotateY(0deg) rotateX(0deg) scale(1)'\">
                        <img src=\"";
        // line 203
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/certificates/certificate-sample.png"), "html", null, true);
        yield "\"
                             alt=\"Certificate Sample\"
                             class=\"img-fluid\"
                             style=\"
                                max-width: 450px;
                                width: 100%;
                                border-radius: 10px;
                                box-shadow: 0 10px 25px rgba(0,0,0,0.3);
                             \"
                             onerror=\"this.src='";
        // line 212
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/placeholders/certificate-placeholder.png"), "html", null, true);
        yield "'\">
                    </div>
                </div>
            </div>

            <!-- Course Cards Row -->
            <div class=\"row\">
                <div class=\"col-12\">
                    <div class=\"course-cards-container\">
                        <div class=\"row g-3 justify-content-center\" style=\"margin-top: 0.5rem;\">
                            ";
        // line 222
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (isset($context["popular_courses"]) || array_key_exists("popular_courses", $context) ? $context["popular_courses"] : (function () { throw new RuntimeError('Variable "popular_courses" does not exist.', 222, $this->source); })()), 0, 4));
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            // line 223
            yield "                            <div class=\"col-lg-3 col-md-6 mb-4\">
                                <div class=\"course-card h-100\" style=\"
                                    background: white;
                                    border-radius: 20px;
                                    overflow: hidden;
                                    box-shadow: 0 10px 30px rgba(1, 26, 45, 0.12);
                                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                    border: 1px solid rgba(255, 255, 255, 0.4);
                                    width: 290px;
                                    height: 340px;
                                    margin: 0 auto 1.5rem auto;
                                    position: relative;
                                    backdrop-filter: blur(15px);
                                    display: flex;
                                    flex-direction: column;
                                \" onmouseover=\"this.style.transform='translateY(-12px) scale(1.05)'; this.style.boxShadow='0 20px 40px rgba(1, 26, 45, 0.2)'; this.querySelector('.course-thumbnail img, .course-thumbnail > div').style.transform='scale(1.1)'\"
                                   onmouseout=\"this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 30px rgba(1, 26, 45, 0.12)'; this.querySelector('.course-thumbnail img, .course-thumbnail > div').style.transform='scale(1)'\"
                                   data-course-card>

                                    <!-- Course Thumbnail -->
                                    <div class=\"course-thumbnail\" style=\"
                                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                                        padding: 1rem;
                                        text-align: center;
                                        flex: 1;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        position: relative;
                                        overflow: hidden;
                                    \">
                                        ";
            // line 254
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailImage", [], "any", false, false, false, 254)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 255
                yield "                                            <img src=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getFunction('course_image_url')->getCallable()(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailImage", [], "any", false, false, false, 255), "thumbnail"), "html", null, true);
                yield "\"
                                                 alt=\"";
                // line 256
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 256), "html", null, true);
                yield "\"
                                                 style=\"
                                                     width: 100%;
                                                     height: 140px;
                                                     object-fit: cover;
                                                     border-radius: 12px;
                                                     border: 2px solid rgba(1, 26, 45, 0.1);
                                                     box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                                                     transition: all 0.4s ease;
                                                 \"
                                                 onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                            <div style=\"
                                                width: 100%;
                                                height: 140px;
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
                                                display: none;
                                                align-items: center;
                                                justify-content: center;
                                                color: white;
                                                border-radius: 12px;
                                                border: 2px solid rgba(1, 26, 45, 0.1);
                                                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                                                transition: all 0.4s ease;
                                                position: relative;
                                                overflow: hidden;
                                            \">
                                                <div class=\"text-center\" style=\"position: relative; z-index: 2;\">
                                                    <i class=\"fas fa-graduation-cap fa-2x mb-2\" style=\"text-shadow: 0 2px 4px rgba(0,0,0,0.3);\"></i>
                                                    <div style=\"font-size: 0.85rem; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);\">";
                // line 284
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 284), "html", null, true);
                yield "</div>
                                                </div>
                                                <div style=\"position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); animation: shimmer 3s infinite;\"></div>
                                            </div>
                                        ";
            } else {
                // line 289
                yield "                                            <div style=\"
                                                width: 100%;
                                                height: 140px;
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 50%, #a90418 100%);
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                color: white;
                                                border-radius: 12px;
                                                border: 2px solid rgba(1, 26, 45, 0.1);
                                                position: relative;
                                                overflow: hidden;
                                                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                                                transition: all 0.4s ease;
                                            \">
                                                <div class=\"text-center\" style=\"position: relative; z-index: 2;\">
                                                    <i class=\"fas fa-graduation-cap fa-2x mb-2\" style=\"text-shadow: 0 2px 4px rgba(0,0,0,0.3);\"></i>
                                                    <div style=\"font-size: 0.85rem; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);\">";
                // line 306
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 306), "html", null, true);
                yield "</div>
                                                </div>
                                                <div style=\"position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); animation: shimmer 3s infinite;\"></div>
                                            </div>
                                        ";
            }
            // line 311
            yield "                                    </div>

                                    <!-- Course Info Footer -->
                                    <div class=\"course-footer\" style=\"
                                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                                        padding: 1.25rem;
                                        border-top: 2px solid rgba(1, 26, 45, 0.08);
                                        margin-top: auto;
                                        position: relative;
                                    \">
                                        <!-- Course Code and Title on Same Line -->
                                        <div class=\"d-flex align-items-center justify-content-between mb-3\">
                                            <span class=\"badge\" style=\"
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
                                                color: white;
                                                font-size: 0.75rem;
                                                font-weight: 600;
                                                padding: 0.4rem 0.8rem;
                                                border-radius: 15px;
                                                flex-shrink: 0;
                                                box-shadow: 0 2px 8px rgba(1, 26, 45, 0.2);
                                                text-transform: uppercase;
                                                letter-spacing: 0.5px;
                                            \">";
            // line 334
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 334), "html", null, true);
            yield "</span>
                                            <div style=\"
                                                font-size: 0.85rem;
                                                font-weight: 600;
                                                color: #011a2d;
                                                line-height: 1.3;
                                                margin-left: 0.75rem;
                                                flex-grow: 1;
                                                text-align: left;
                                                font-family: 'Montserrat', sans-serif;
                                            \">
                                                ";
            // line 345
            yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 345)) > 22)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 345), 0, 22) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 345), "html", null, true)));
            yield "
                                            </div>
                                        </div>

                                        <!-- Learn More Button -->
                                        <div class=\"text-center\">
                                            <a href=\"";
            // line 351
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_course_show", ["code" => CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 351)]), "html", null, true);
            yield "\" class=\"btn btn-sm w-100\" style=\"
                                                background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
                                                color: white;
                                                border: none;
                                                border-radius: 25px;
                                                font-size: 0.85rem;
                                                font-weight: 600;
                                                padding: 0.65rem 1.25rem;
                                                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                                box-shadow: 0 4px 15px rgba(169, 4, 24, 0.2);
                                                position: relative;
                                                overflow: hidden;
                                                font-family: 'Montserrat', sans-serif;
                                                letter-spacing: 0.5px;
                                                text-transform: uppercase;
                                            \" onmouseover=\"this.style.transform='translateY(-3px) scale(1.05)'; this.style.boxShadow='0 6px 20px rgba(169, 4, 24, 0.3)'; this.querySelector('.btn-shimmer').style.left='100%'\"
                                               onmouseout=\"this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 4px 15px rgba(169, 4, 24, 0.2)'; this.querySelector('.btn-shimmer').style.left='-100%'\">
                                                <span style=\"position: relative; z-index: 2;\">
                                                    <i class=\"fas fa-arrow-right me-2\"></i>Learn More
                                                </span>
                                                <div class=\"btn-shimmer\" style=\"position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.6s ease; z-index: 1;\"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 378
        yield "                        </div>

                        <!-- Enhanced Explore Courses Button -->
                        <div class=\"text-center mt-5 mb-4\">
                            <a href=\"";
        // line 382
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_courses");
        yield "\" class=\"btn btn-lg px-5 py-3\" style=\"
                                border: 2px solid #971020;
                                border-radius: 8px;
                                font-weight: 600;
                                background: #971020;
                                color: white;
                                text-decoration: none;
                                transition: all 0.3s ease;
                                font-family: 'Montserrat', sans-serif;

                            \" onmouseover=\"this.style.background='white'; this.style.color='#971020'; this.style.transform='translateY(-2px)'\"
                               onmouseout=\"this.style.background='#971020'; this.style.color='white'; this.style.transform='translateY(0)'\">
                                <span style=\"position: relative; z-index: 1;\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>Explore Our Courses
                                </span>
                                <div style=\"position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.5s;\"></div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Free Trading Stream -->
    <section class=\"trading-stream-section position-relative\" style=\"
        background-image: url('";
        // line 408
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background 3 Free Trading Stream.png"), "html", null, true);
        yield "');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
    \">
        <!-- Overlay -->
        <div class=\"position-absolute top-0 start-0 w-100 h-100\" style=\"background: rgba(0, 0, 0, 0.6); z-index: 1;\"></div>

        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <div class=\"row align-items-center\">
                <!-- Left: Content -->
                <div class=\"col-lg-6 text-white mb-4 mb-lg-0 pe-5\">
                    <h2 class=\"fw-bold mb-4\" style=\"font-size: 2.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); font-family: 'Montserrat', sans-serif;\">Free Trading Stream</h2>
                    <p class=\"mb-0\" style=\"font-size: 1.3rem; line-height: 1.6; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); font-family: 'Calibri', Arial, sans-serif;\">
                        Join our experts for real-time market analysis, detailed trade breakdowns, and expert guidance. From beginners to seasoned traders, our stream provides the knowledge you need to make informed decisions and improve your trading skills.
                    </p>
                </div>

                <!-- Right: YouTube Video Embed - Smaller Size -->
                <div class=\"col-lg-6\">
                    <div class=\"video-embed-container position-relative\" style=\"
                        border-radius: 15px;
                        overflow: hidden;
                        box-shadow: 0 15px 30px rgba(0,0,0,0.4);
                        background: #000;
                        aspect-ratio: 16/9;
                        max-width: 450px;
                        margin: 0 auto;
                    \">
                        <iframe
                            src=\"https://www.youtube.com/embed/3ox5jXJvRpU?rel=0&modestbranding=1&showinfo=0\"
                            title=\"Free Trading Stream\"
                            frameborder=\"0\"
                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"
                            allowfullscreen
                            style=\"
                                width: 100%;
                                height: 100%;
                                border-radius: 15px;
                            \">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- Past Trading Stream Content within same section -->
        <div class=\"container position-relative\" style=\"z-index: 2; margin-top: 4rem;\">
            <div class=\"row\">
                <div class=\"col-12 text-white mb-3\">
                    <h2 class=\"fw-bold mb-2\" style=\"font-size: 2rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); font-family: 'Montserrat', sans-serif;\">Past Trading Stream</h2>
                    <!-- White horizontal line under title -->
                    <div style=\"width: 100%; height: 2px; background: white; margin-bottom: 1.5rem;\"></div>
                </div>
            </div>

            <!-- 4 Video Thumbnails in a Row -->
            <div class=\"row g-4 justify-content-center\">
                ";
        // line 468
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["past_videos"]) || array_key_exists("past_videos", $context) ? $context["past_videos"] : (function () { throw new RuntimeError('Variable "past_videos" does not exist.', 468, $this->source); })())) > 0)) {
            // line 469
            yield "                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (isset($context["past_videos"]) || array_key_exists("past_videos", $context) ? $context["past_videos"] : (function () { throw new RuntimeError('Variable "past_videos" does not exist.', 469, $this->source); })()), 0, 4));
            foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
                // line 470
                yield "                    <div class=\"col-lg-3 col-md-6\">
                        <div class=\"video-card position-relative\" style=\"
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 10px 25px rgba(0,0,0,0.4);
                            transition: all 0.3s ease;
                            cursor: pointer;
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255,255,255,0.2);
                        \" onmouseover=\"this.style.transform='translateY(-8px) scale(1.02)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.6)'\"
                           onmouseout=\"this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.4)'\"
                           onclick=\"openVideoPlayer(";
                // line 482
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "id", [], "any", false, false, false, 482), "html", null, true);
                yield ")\">
                            <div class=\"video-thumbnail position-relative\" style=\"height: 160px;\">
                                ";
                // line 484
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "thumbnail", [], "any", false, false, false, 484)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 485
                    yield "                                    <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, $context["video"], "thumbnail", [], "any", false, false, false, 485))), "html", null, true);
                    yield "\"
                                         alt=\"";
                    // line 486
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 486), "html", null, true);
                    yield "\"
                                         class=\"w-100 h-100\"
                                         style=\"object-fit: cover;\"
                                         onerror=\"this.src='";
                    // line 489
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/placeholders/video-placeholder.jpg"), "html", null, true);
                    yield "'\">
                                ";
                } else {
                    // line 491
                    yield "                                    <div class=\"w-100 h-100\" style=\"background: linear-gradient(135deg, #011a2d 0%, #a90418 100%); display: flex; align-items: center; justify-content: center;\">
                                        <i class=\"fas fa-video text-white\" style=\"font-size: 2rem; opacity: 0.7;\"></i>
                                    </div>
                                ";
                }
                // line 495
                yield "
                                <!-- Play overlay -->
                                <div class=\"video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\" style=\"
                                    background: rgba(0,0,0,0.3);
                                    transition: all 0.3s ease;
                                \">
                                    <div class=\"play-button text-center\">
                                        <div class=\"play-icon\" style=\"
                                            width: 50px;
                                            height: 50px;
                                            background: rgba(255,255,255,0.9);
                                            border-radius: 50%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin: 0 auto;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                                        \">
                                            <i class=\"fas fa-play\" style=\"font-size: 1.2rem; margin-left: 2px; color: #011a2d;\"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class=\"video-info p-3 text-white\">
                                <h6 class=\"fw-bold mb-1\" style=\"font-size: 0.9rem; line-height: 1.3;\">";
                // line 520
                yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 520)) > 25)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 520), 0, 25) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 520), "html", null, true)));
                yield "</h6>
                                <p class=\"small text-white-50 mb-0\">";
                // line 521
                yield (((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", true, true, false, 521) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 521)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 521), "html", null, true)) : ("Trading Stream"));
                yield "</p>
                            </div>
                        </div>
                    </div>
                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 526
            yield "                ";
        } else {
            // line 527
            yield "                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 4));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 528
                yield "                    <div class=\"col-lg-3 col-md-6\">
                        <div class=\"video-card position-relative\" style=\"
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 10px 25px rgba(0,0,0,0.4);
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255,255,255,0.2);
                        \">
                            <div class=\"video-thumbnail position-relative\" style=\"height: 160px; background: linear-gradient(135deg, #011a2d 0%, #a90418 100%); display: flex; align-items: center; justify-content: center;\">
                                <i class=\"fas fa-video text-white\" style=\"font-size: 2rem; opacity: 0.7;\"></i>
                            </div>
                            <div class=\"video-info p-3 text-white\">
                                <h6 class=\"fw-bold mb-1\" style=\"font-size: 0.9rem;\">Sample Video ";
                // line 541
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["i"], "html", null, true);
                yield "</h6>
                                <p class=\"small text-white-50 mb-0\">Trading Stream</p>
                            </div>
                        </div>
                    </div>
                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 547
            yield "                ";
        }
        // line 548
        yield "            </div>

            <!-- Explore Videos Button -->
            <div class=\"text-center mt-5\">
                <a href=\"";
        // line 552
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos");
        yield "\" class=\"btn btn-outline-light btn-lg px-5 py-3\" style=\"
                    border: 2px solid white;
                    border-radius: 8px;
                    font-weight: 600;
                    background: transparent;
                    color: white;
                    transition: all 0.3s ease;
                    font-family: 'Montserrat', sans-serif;
                    margin-top: 30px;
                    margin-bottom: 20px;
                \" onmouseover=\"this.style.background='white'; this.style.color='#00233e'; this.style.transform='translateY(-2px)'\"
                   onmouseout=\"this.style.background='transparent'; this.style.color='white'; this.style.transform='translateY(0)'\">
                    <i class=\"fas fa-video me-2\"></i>Explore Our Videos
                </a>
            </div>

        </div>
    </section>

    <!-- New Analysis Section -->
    <section class=\"analysis-section position-relative\" style=\"
        background-image: url('";
        // line 573
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background Get your Analysis HP.png"), "html", null, true);
        yield "');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
        min-height: 100vh;
    \">
        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <!-- Centered Title with Top Margin -->
            <div class=\"row mb-5\" style=\"margin-top: 2rem;\">
                <div class=\"col-12 text-center\">
                    <h2 class=\"fw-bold\" style=\"
                        font-size: 3rem;
                        color: #00233e;
                        font-family: 'Montserrat', sans-serif;
                    \">Get Your ANALYSIS Today!</h2>
                </div>
            </div>

            <div class=\"row align-items-center\">
                <!-- Left: Content with proper spacing -->
                <div class=\"col-lg-6 mb-4 mb-lg-0 pe-5\">
                    <h4 class=\"fw-bold mb-4\" style=\"
                        color: #971020;
                        font-family: 'Montserrat', sans-serif;
                        font-size: 2.2rem;
                    \">Register on our Analysis</h4>

                    <p class=\"mb-4\" style=\"
                        font-size: 1.4rem;
                        line-height: 1.7;
                        color: #45403f;
                        font-family: 'Calibri', Arial, sans-serif;
                    \">
                        Get personalized financial market analysis from our expert team. Our comprehensive analysis covers market trends, technical indicators, risk assessment, and strategic recommendations tailored to your investment goals. Join thousands of successful traders who rely on our professional insights to make informed trading decisions.
                    </p>
                </div>

                <!-- Right: Email Form with more spacing and smaller container -->
                <div class=\"col-lg-6 ps-5\">
                    <div class=\"d-flex justify-content-center\">
                        <div class=\"analysis-form-container\" style=\"
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 20px;
                            padding: 25px;
                            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
                            backdrop-filter: blur(10px);
                            max-width: 400px;
                            width: 100%;
                        \">
                        <form class=\"analysis-form\">
                            <div class=\"mb-3\">
                                <input type=\"text\" class=\"form-control\" placeholder=\"Full Name\" style=\"
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                \" onfocus=\"this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'\"
                                   onblur=\"this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'\">
                            </div>
                            <div class=\"mb-3\">
                                <input type=\"email\" class=\"form-control\" placeholder=\"Email Address\" style=\"
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                \" onfocus=\"this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'\"
                                   onblur=\"this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'\">
                            </div>
                            <div class=\"mb-3\">
                                <input type=\"tel\" class=\"form-control\" placeholder=\"Phone Number\" style=\"
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                \" onfocus=\"this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'\"
                                   onblur=\"this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'\">
                            </div>
                            <button type=\"submit\" class=\"btn w-100 py-3 fw-bold\" style=\"
                                background: #a90418;
                                color: white;
                                border: none;
                                border-radius: 15px;
                                font-size: 1.1rem;
                                transition: all 0.3s ease;
                                padding: 15px 20px;
                            \" onmouseover=\"this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(169,4,24,0.4)'\"
                               onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='none'\">
                                <i class=\"fas fa-chart-line me-2\"></i>Get Analysis
                            </button>
                        </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- New Trading Made Easy Section -->
    <section class=\"trading-made-easy-section position-relative\" style=\"
        background-image: url('";
        // line 680
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background Trading Made Easy HP.png"), "html", null, true);
        yield "');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
        min-height: 100vh;
    \">
        <!-- Overlay -->
        <div class=\"position-absolute top-0 start-0 w-100 h-100\" style=\"background: rgba(0, 0, 0, 0.3); z-index: 1;\"></div>

        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <!-- Centered Container with Top Margin -->
            <div class=\"row justify-content-start\" style=\"margin-top: 3rem;\">
                <div class=\"col-lg-8 col-xl-6\">
                    <div class=\"glassmorphism-container\" style=\"
                        background: rgba(255, 255, 255, 0.08);
                        backdrop-filter: blur(15px);
                        border-radius: 25px;
                        padding: 50px;
                        border: 2px solid rgba(255, 255, 255, 0.15);
                        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                        text-align: left;
                        max-width: 600px;
                        margin: 0;
                    \">
                        <h2 class=\"fw-bold text-white\" style=\"
                            font-size: 2.8rem;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
                            font-family: 'Montserrat', sans-serif;
                            margin-bottom: 2rem;
                        \">Trading Made Easy!</h2>

                        <p class=\"text-white\" style=\"
                            font-size: 1.3rem;
                            line-height: 1.8;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                            font-family: 'Calibri', Arial, sans-serif;
                            margin-bottom: 2rem;
                        \">
                            Learn Technical Analysis at your own pace with professional instructors.
                        </p>

                        <p class=\"text-white\" style=\"
                            font-size: 1.3rem;
                            line-height: 1.8;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                            font-family: 'Calibri', Arial, sans-serif;
                            margin-bottom: 3rem;
                        \">
                            Register now and Discover Financial Freedom
                        </p>

                        <!-- Email Input with Integrated Button -->
                        <div class=\"email-input-container position-relative\" style=\"max-width: 500px;\">
                            <div class=\"input-group position-relative\" style=\"border-radius: 25px; overflow: hidden; box-shadow: 0 8px 25px rgba(0,0,0,0.2);\">
                                <input type=\"email\" class=\"form-control\" placeholder=\"Your Email Address ...\" style=\"
                                    border: none;
                                    padding: 18px 120px 18px 20px;
                                    font-size: 1rem;
                                    background: white;
                                    border-radius: 25px;
                                    width: 100%;
                                \">
                                <a href=\"";
        // line 744
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_registration");
        yield "\" class=\"btn position-absolute\" style=\"
                                    background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 20px;
                                    font-size: 0.9rem;
                                    font-weight: 600;
                                    text-decoration: none;
                                    border-radius: 20px;
                                    transition: all 0.3s ease;
                                    display: flex;
                                    align-items: center;
                                    right: 6px;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    z-index: 10;
                                \" onmouseover=\"this.style.background='linear-gradient(135deg, #8b0314 0%, #6d0210 100%)'\"
                                   onmouseout=\"this.style.background='linear-gradient(135deg, #a90418 0%, #8b0314 100%)'\">
                                    <i class=\"fas fa-rocket me-2\"></i>Start Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 775
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 776
        yield "<style>
/* Enhanced seamless partner carousel animation */
@keyframes scroll-partners-seamless {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.partners-carousel {
    animation: scroll-partners-seamless 20s linear infinite;
    white-space: nowrap;
    will-change: transform;
    display: flex;
    align-items: center;
}

.partners-carousel-container {
    mask: linear-gradient(90deg, transparent, white 10%, white 90%, transparent);
    -webkit-mask: linear-gradient(90deg, transparent, white 10%, white 90%, transparent);
}

.partner-set {
    flex-shrink: 0;
}

.partner-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.partner-img {
    max-width: 100%;
    height: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects for partner logos */
.partner-logo:hover .partner-img {
    filter: grayscale(0%) brightness(1) !important;
    transform: scale(1.1) !important;
    opacity: 1 !important;
}

/* Responsive partner carousel */
@media (max-width: 768px) {
    .partner-logo {
        min-width: 100px !important;
        margin: 0 0.75rem !important;
    }

    .partner-img {
        height: 35px !important;
    }

    .partners-carousel {
        animation-duration: 15s !important;
    }
}

/* Floating animation for background elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Hero section responsive adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.1rem !important;
    }

    .stock-ticker .ticker-content {
        font-size: 0.8rem;
    }

    .countdown-timer {
        font-size: 0.8rem;
    }
}

/* Course cards hover effects */
.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.4) !important;
}

/* Video cards hover effects */
.video-card:hover .play-icon {
    transform: scale(1.1);
    background: rgba(255,255,255,0.3) !important;
}

/* Form focus effects */
.form-control:focus, .form-select:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Background parallax effect */
.hero-section,
.courses-section,
.trading-stream-section,
.past-streams-section,
.signup-section {
    background-attachment: fixed;
}

@media (max-width: 768px) {
    .hero-section,
    .courses-section,
    .trading-stream-section,
    .past-streams-section,
    .signup-section {
        background-attachment: scroll;
    }
}

/* Loading animation for images */
img {
    transition: opacity 0.3s ease;
}

/* Ensure footer social media icons are circular on homepage */
footer .footer-social-btn {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid #a90418 !important;
    color: #a90418 !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
}

footer .footer-social-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3) !important;
    background: #a90418 !important;
    border-color: #a90418 !important;
    color: white !important;
}

/* Gradient text animation */
@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Shimmer animation for course cards */
@keyframes shimmer {
    0% {
        transform: rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: rotate(180deg);
        opacity: 0.7;
    }
    100% {
        transform: rotate(360deg);
        opacity: 0.3;
    }
}

.gradient-text {
    background: linear-gradient(-45deg, #00ff00, #ff0000, #00ff00, #ff0000);
    background-size: 400% 400%;
    animation: gradient-shift 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Our Courses Section Responsive Styles */
@media (max-width: 768px) {
    .courses-section h2 {
        font-size: 2.5rem !important;
    }

    .courses-section h3 {
        font-size: 1.5rem !important;
    }

    .courses-section p {
        font-size: 1rem !important;
    }

    .course-card {
        width: 260px !important;
        height: 300px !important;
        margin-bottom: 1.2rem !important;
    }

    .certificate-container {
        margin-top: 2rem;
        transform: rotate(0deg) !important;
    }

    .certificate-container:hover {
        transform: rotate(0deg) scale(1.02) !important;
    }

    .course-card {
        margin-bottom: 2rem !important;
        width: 320px !important;
        height: 320px !important;
    }

    .course-header {
        min-height: 45px !important;
        padding: 0.6rem !important;
    }

    .course-header h5 {
        font-size: 0.8rem !important;
    }

    .course-thumbnail {
        padding: 0.8rem !important;
    }

    .course-thumbnail img,
    .course-thumbnail div {
        height: 140px !important;
    }

    .course-footer {
        padding: 0.8rem !important;
    }

    .course-footer .row .col-6 div {
        font-size: 0.75rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    .course-footer .row .col-6 div i {
        font-size: 0.7rem !important;
    }
}

@media (max-width: 576px) {
    .courses-section h2 {
        font-size: 2rem !important;
    }

    .courses-section h3 {
        font-size: 1.3rem !important;
    }

    .course-card {
        width: 240px !important;
        height: 280px !important;
        margin-bottom: 1rem !important;
    }

    .course-thumbnail img,
    .course-thumbnail div {
        height: 120px !important;
    }

    .course-header {
        min-height: 40px !important;
        padding: 0.5rem !important;
    }

    .course-header h5 {
        font-size: 0.75rem !important;
    }

    .course-footer {
        padding: 0.8rem !important;
    }

    .course-footer .row .col-6 div {
        font-size: 0.7rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    .course-footer .row .col-6 div i {
        font-size: 0.65rem !important;
        margin-bottom: 0.1rem !important;
    }

    .course-footer .row .col-6 div div {
        font-size: 0.65rem !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 1093
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 1094
        yield "<script>
// Homepage specific functionality - countdown timer removed to avoid conflicts with promotional banner

// Enhanced seamless partner carousel
document.addEventListener('DOMContentLoaded', function() {
    const partnersCarousel = document.getElementById('partnersCarousel');

    if (partnersCarousel) {
        // Ensure seamless scrolling by monitoring animation
        partnersCarousel.addEventListener('animationiteration', function() {
            // This event fires each time the animation completes a cycle
            // The CSS animation automatically resets to 0, creating seamless loop
        });

        // Continuous scrolling - no pause on hover

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            partnersCarousel.style.animation = 'none';
            partnersCarousel.style.transform = 'translateX(0)';
        }
    }
});

// Form submission handlers
document.addEventListener('DOMContentLoaded', function() {
    // Analysis form submission
    const analysisForm = document.querySelector('.analysis-form');
    if (analysisForm) {
        analysisForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type=\"submit\"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Processing...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class=\"fas fa-check me-2\"></i>Success!';
                submitBtn.style.background = '#28a745';

                // Reset form
                setTimeout(() => {
                    this.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = '#a90418';
                    submitBtn.disabled = false;
                }, 2000);
            }, 1500);
        });
    }

    // Newsletter form submission (if any remaining)
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type=\"submit\"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Processing...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class=\"fas fa-check me-2\"></i>Success!';
                submitBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

                // Reset form
                setTimeout(() => {
                    this.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = 'linear-gradient(135deg, #a90418 0%, #011a2d 100%)';
                    submitBtn.disabled = false;
                }, 2000);
            }, 1500);
        });
    }
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animatedElements = document.querySelectorAll('.course-card, .video-card, .benefit-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Video play functionality
function playVideo(container) {
    const overlay = container.querySelector('.video-overlay');
    if (overlay) {
        overlay.innerHTML = '<div class=\"text-center\"><i class=\"fas fa-spinner fa-spin text-white\" style=\"font-size: 2rem;\"></i><p class=\"text-white mt-2\">Loading video...</p></div>';

        // Simulate loading
        setTimeout(() => {
            overlay.innerHTML = '<div class=\"text-center\"><i class=\"fas fa-play text-white\" style=\"font-size: 2rem;\"></i><p class=\"text-white mt-2\">Video would play here</p></div>';
        }, 1500);
    }
}

// Open video player in new tab/window
function openVideoPlayer(videoId) {
    if (videoId) {
        // Open video in new tab
        window.open('/videos/' + videoId, '_blank');
    } else {
        // Fallback for demo videos
        alert('Video player would open here. This is a demo video.');
    }
}

// Video play button interactions
document.querySelectorAll('.play-button, .play-icon').forEach(button => {
    button.addEventListener('click', function(e) {
        e.stopPropagation();
        playVideo(this.closest('.video-container, .video-card'));
    });
});

// Dynamic stats animation
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (stat.textContent.includes('+') || stat.textContent.includes('%')) {
            const originalText = stat.textContent;
            let counter = 0;
            const target = parseFloat(originalText.replace(/[^\\d.-]/g, ''));
            const increment = target / 50;

            const timer = setInterval(() => {
                counter += increment;
                if (counter >= target) {
                    stat.textContent = originalText;
                    clearInterval(timer);
                } else {
                    stat.textContent = originalText.replace(target.toString(), Math.floor(counter).toString());
                }
            }, 50);
        }
    });
}

// Trigger stats animation when section is visible
const statsSection = document.querySelector('.trading-stream-section');
if (statsSection) {
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStats();
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statsObserver.observe(statsSection);
}

// Course Cards Animation
document.addEventListener('DOMContentLoaded', function() {
    // Animate course cards on scroll
    const courseCards = document.querySelectorAll('.course-card');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                cardObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    courseCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "home/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1396 => 1094,  1383 => 1093,  1057 => 776,  1044 => 775,  1003 => 744,  936 => 680,  826 => 573,  802 => 552,  796 => 548,  793 => 547,  781 => 541,  766 => 528,  761 => 527,  758 => 526,  747 => 521,  743 => 520,  716 => 495,  710 => 491,  705 => 489,  699 => 486,  694 => 485,  692 => 484,  687 => 482,  673 => 470,  668 => 469,  666 => 468,  603 => 408,  574 => 382,  568 => 378,  535 => 351,  526 => 345,  512 => 334,  487 => 311,  479 => 306,  460 => 289,  452 => 284,  421 => 256,  416 => 255,  414 => 254,  381 => 223,  377 => 222,  364 => 212,  352 => 203,  296 => 150,  284 => 141,  271 => 130,  264 => 125,  260 => 123,  238 => 109,  235 => 108,  231 => 107,  225 => 103,  203 => 89,  200 => 88,  196 => 87,  192 => 85,  189 => 84,  187 => 83,  155 => 54,  110 => 12,  102 => 6,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Capitol Academy - Professional Financial Training{% endblock %}

{% block body %}

<main class=\"homepage-main\">
    <!-- Section 1: Hero Section -->
    <section class=\"hero-section position-relative overflow-hidden\" style=\"height: 80vh;\">
        <!-- Background Image -->
        <div class=\"hero-background\" style=\"
            background-image: url('{{ asset('images/backgrounds/Banner 1 HP.jpeg') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        \"></div>

        <!-- Light Overlay for Text Readability -->
        <div class=\"hero-overlay\" style=\"
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(1, 26, 45, 0.2);
            z-index: 2;
        \"></div>

        <!-- Hero Content -->
        <div class=\"container position-relative\" style=\"z-index: 3; height: 80vh;\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-8\">
                    <div class=\"hero-content text-white py-4\">
                        <h1 class=\"hero-title fw-bold mb-4\" style=\"
                            font-size: 3.5rem;
                            line-height: 1.2;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                            color: white;
                            font-family: 'Montserrat', sans-serif;
                        \">
                            <div class=\"mb-2\">You Can LEARN.</div>
                            <div class=\"mb-2\">We Can TEACH.</div>
                            <div class=\"mb-2\">We'll Be Great.</div>
                            <div>TOGETHER.</div>
                        </h1>

                        <div class=\"hero-actions\">
                            <a href=\"{{ path('app_contact_registration') }}\" class=\"btn btn-outline-light px-3 py-3\" style=\"
                                border: 2px solid white;
                                background: transparent;
                                color: white;
                                font-weight: 600;
                                border-radius: 8px;
                                transition: all 0.3s ease;
                                font-size: 1.1rem;
                            \" onmouseover=\"this.style.background='white'; this.style.color='#011a2d'; this.style.borderColor='white'; this.style.borderRadius='8px';\"
                               onmouseout=\"this.style.background='transparent'; this.style.color='white'; this.style.borderColor='white'; this.style.borderRadius='8px';\">
                                <i class=\"fas fa-user-plus me-2\"></i>Register Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 2: Trusted Partners -->
    <section class=\"partners-section py-4\" style=\"background: white; margin: 0; padding-top: 2rem; padding-bottom: 2rem;\">
        <div class=\"container position-relative\">
            <!-- Inline Trusted by text with logos -->
            <div class=\"d-flex align-items-center justify-content-center\">
                <h3 class=\"fw-bold text-dark me-5 mb-2\" style=\"font-size: 1.5rem; white-space: nowrap; margin-right: 3rem !important;\">Trusted by</h3>

                <!-- Enhanced Seamless Scrolling Partners Logos -->
                <div class=\"partners-carousel-container overflow-hidden position-relative flex-grow-1\" style=\"height: 50px;\">
                    <div class=\"partners-carousel\" id=\"partnersCarousel\">
                        {% set partners = get_active_partners() %}
                        {% if partners|length > 0 %}
                            <!-- First complete set of logos -->
                            <div class=\"partner-set d-flex align-items-center\">
                                {% for partner in partners %}
                                <div class=\"partner-logo flex-shrink-0 mx-4 d-flex align-items-center justify-content-center\" style=\"min-width: 80px; height: 50px;\">
                                    <img src=\"{{ partner.logoUrl }}\" alt=\"{{ partner.name }}\"
                                         class=\"img-fluid partner-img\"
                                         style=\"
                                             height: 35px;
                                             opacity: 1;
                                             transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                             object-fit: contain;
                                             filter: grayscale(0%) brightness(1);
                                         \"
                                         onmouseover=\"this.style.transform='scale(1.1)'\"
                                         onmouseout=\"this.style.transform='scale(1)'\"
                                         onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                                </div>
                                {% endfor %}
                            </div>

                            <!-- Exact duplicate set for seamless infinite loop -->
                            <div class=\"partner-set d-flex align-items-center\">
                                {% for partner in partners %}
                                <div class=\"partner-logo flex-shrink-0 mx-4 d-flex align-items-center justify-content-center\" style=\"min-width: 80px; height: 50px;\">
                                    <img src=\"{{ partner.logoUrl }}\" alt=\"{{ partner.name }}\"
                                         class=\"img-fluid partner-img\"
                                         style=\"
                                             height: 35px;
                                             opacity: 1;
                                             transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                             object-fit: contain;
                                             filter: grayscale(0%) brightness(1);
                                         \"
                                         onmouseover=\"this.style.transform='scale(1.1)'\"
                                         onmouseout=\"this.style.transform='scale(1)'\"
                                         onerror=\"this.src='/images/placeholders/image-placeholder.png'\">
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <!-- Fallback if no partners in database -->
                            <div class=\"text-center w-100 d-flex align-items-center justify-content-center\" style=\"height: 50px;\">
                                <p class=\"text-muted mb-0\">Partner logos will appear here</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>



        </div>
    </section>

    <!-- Section 3: Our Courses -->
    <section class=\"courses-section position-relative\" style=\"
        background-image: url('{{ asset('images/backgrounds/fond-sitemap.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        margin-top: 2rem;
        padding: 4rem 0;
    \">
        <!-- Enhanced Darker Overlay for Better Contrast -->
        <div class=\"position-absolute top-0 start-0 w-100 h-100\" style=\"background-image: url('{{ asset('images/backgrounds/fond-sitemap.png') }}'); border: 10px solid rgba(255, 255, 255, 0.1); z-index: 1;\"></div>

        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <!-- Enhanced Main Section Title -->
            <div class=\"row mb-5\">
                <div class=\"col-12 text-center\">
                    <h2 class=\"fw-bold mb-4\" style=\"
                        font-size: 2.5rem;
                        color: #00233e;
                        margin-bottom: 2rem;
                        font-family: 'Montserrat', sans-serif;
                        font-weight: 700;
                        line-height: 1.1;
                    \">Our Courses</h2>
                </div>
            </div>

            <!-- Content Area -->
            <div class=\"row mb-5 align-items-center\">
                <!-- Left: Text Content -->
                <div class=\"col-lg-7 pe-5 d-flex flex-column justify-content-center\">
                    <h3 class=\"fw-bold mb-4\" style=\"
                        font-size: 1.3rem;
                        color: #971020;
                        font-family: 'Montserrat', sans-serif;
                        font-weight: 600;
                        line-height: 1.2;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    \">Financial Market Technicals Analysis Certified Courses</h3>

                    <p class=\"mb-4\" style=\"
                        font-size: 1.4rem;
                        line-height: 1.6;
                        color: #45403f;
                        font-family: 'Calibri', Arial, sans-serif;
                        font-weight: 400;
                        margin-right: 3rem;
                    \">
                        From finance fundamentals to advanced deep-dives, each course offers video lessons, practical templates,
                        interactive exercises, skill validation, and a certificate of completion. Over 75% of Capitol Academy
                        learners report improved productivity or competency within weeks.
                    </p>
                </div>

                <!-- Right: Certificate Image -->
                <div class=\"col-lg-5 d-flex align-items-center justify-content-center ps-4\">
                    <div class=\"certificate-container\" style=\"
                        transition: all 0.3s ease;
                        transform-style: preserve-3d;
                    \" onmouseover=\"this.style.transform='rotateY(5deg) rotateX(5deg) scale(1.05)'\"
                       onmouseout=\"this.style.transform='rotateY(0deg) rotateX(0deg) scale(1)'\">
                        <img src=\"{{ asset('images/certificates/certificate-sample.png') }}\"
                             alt=\"Certificate Sample\"
                             class=\"img-fluid\"
                             style=\"
                                max-width: 450px;
                                width: 100%;
                                border-radius: 10px;
                                box-shadow: 0 10px 25px rgba(0,0,0,0.3);
                             \"
                             onerror=\"this.src='{{ asset('images/placeholders/certificate-placeholder.png') }}'\">
                    </div>
                </div>
            </div>

            <!-- Course Cards Row -->
            <div class=\"row\">
                <div class=\"col-12\">
                    <div class=\"course-cards-container\">
                        <div class=\"row g-3 justify-content-center\" style=\"margin-top: 0.5rem;\">
                            {% for course in popular_courses|slice(0, 4) %}
                            <div class=\"col-lg-3 col-md-6 mb-4\">
                                <div class=\"course-card h-100\" style=\"
                                    background: white;
                                    border-radius: 20px;
                                    overflow: hidden;
                                    box-shadow: 0 10px 30px rgba(1, 26, 45, 0.12);
                                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                    border: 1px solid rgba(255, 255, 255, 0.4);
                                    width: 290px;
                                    height: 340px;
                                    margin: 0 auto 1.5rem auto;
                                    position: relative;
                                    backdrop-filter: blur(15px);
                                    display: flex;
                                    flex-direction: column;
                                \" onmouseover=\"this.style.transform='translateY(-12px) scale(1.05)'; this.style.boxShadow='0 20px 40px rgba(1, 26, 45, 0.2)'; this.querySelector('.course-thumbnail img, .course-thumbnail > div').style.transform='scale(1.1)'\"
                                   onmouseout=\"this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 30px rgba(1, 26, 45, 0.12)'; this.querySelector('.course-thumbnail img, .course-thumbnail > div').style.transform='scale(1)'\"
                                   data-course-card>

                                    <!-- Course Thumbnail -->
                                    <div class=\"course-thumbnail\" style=\"
                                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                                        padding: 1rem;
                                        text-align: center;
                                        flex: 1;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        position: relative;
                                        overflow: hidden;
                                    \">
                                        {% if course.thumbnailImage %}
                                            <img src=\"{{ course_image_url(course.thumbnailImage, 'thumbnail') }}\"
                                                 alt=\"{{ course.title }}\"
                                                 style=\"
                                                     width: 100%;
                                                     height: 140px;
                                                     object-fit: cover;
                                                     border-radius: 12px;
                                                     border: 2px solid rgba(1, 26, 45, 0.1);
                                                     box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                                                     transition: all 0.4s ease;
                                                 \"
                                                 onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                            <div style=\"
                                                width: 100%;
                                                height: 140px;
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
                                                display: none;
                                                align-items: center;
                                                justify-content: center;
                                                color: white;
                                                border-radius: 12px;
                                                border: 2px solid rgba(1, 26, 45, 0.1);
                                                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                                                transition: all 0.4s ease;
                                                position: relative;
                                                overflow: hidden;
                                            \">
                                                <div class=\"text-center\" style=\"position: relative; z-index: 2;\">
                                                    <i class=\"fas fa-graduation-cap fa-2x mb-2\" style=\"text-shadow: 0 2px 4px rgba(0,0,0,0.3);\"></i>
                                                    <div style=\"font-size: 0.85rem; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);\">{{ course.code }}</div>
                                                </div>
                                                <div style=\"position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); animation: shimmer 3s infinite;\"></div>
                                            </div>
                                        {% else %}
                                            <div style=\"
                                                width: 100%;
                                                height: 140px;
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 50%, #a90418 100%);
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                color: white;
                                                border-radius: 12px;
                                                border: 2px solid rgba(1, 26, 45, 0.1);
                                                position: relative;
                                                overflow: hidden;
                                                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                                                transition: all 0.4s ease;
                                            \">
                                                <div class=\"text-center\" style=\"position: relative; z-index: 2;\">
                                                    <i class=\"fas fa-graduation-cap fa-2x mb-2\" style=\"text-shadow: 0 2px 4px rgba(0,0,0,0.3);\"></i>
                                                    <div style=\"font-size: 0.85rem; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);\">{{ course.code }}</div>
                                                </div>
                                                <div style=\"position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%); animation: shimmer 3s infinite;\"></div>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Course Info Footer -->
                                    <div class=\"course-footer\" style=\"
                                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                                        padding: 1.25rem;
                                        border-top: 2px solid rgba(1, 26, 45, 0.08);
                                        margin-top: auto;
                                        position: relative;
                                    \">
                                        <!-- Course Code and Title on Same Line -->
                                        <div class=\"d-flex align-items-center justify-content-between mb-3\">
                                            <span class=\"badge\" style=\"
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
                                                color: white;
                                                font-size: 0.75rem;
                                                font-weight: 600;
                                                padding: 0.4rem 0.8rem;
                                                border-radius: 15px;
                                                flex-shrink: 0;
                                                box-shadow: 0 2px 8px rgba(1, 26, 45, 0.2);
                                                text-transform: uppercase;
                                                letter-spacing: 0.5px;
                                            \">{{ course.code }}</span>
                                            <div style=\"
                                                font-size: 0.85rem;
                                                font-weight: 600;
                                                color: #011a2d;
                                                line-height: 1.3;
                                                margin-left: 0.75rem;
                                                flex-grow: 1;
                                                text-align: left;
                                                font-family: 'Montserrat', sans-serif;
                                            \">
                                                {{ course.title|length > 22 ? course.title|slice(0, 22) ~ '...' : course.title }}
                                            </div>
                                        </div>

                                        <!-- Learn More Button -->
                                        <div class=\"text-center\">
                                            <a href=\"{{ path('app_course_show', {code: course.code}) }}\" class=\"btn btn-sm w-100\" style=\"
                                                background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
                                                color: white;
                                                border: none;
                                                border-radius: 25px;
                                                font-size: 0.85rem;
                                                font-weight: 600;
                                                padding: 0.65rem 1.25rem;
                                                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                                box-shadow: 0 4px 15px rgba(169, 4, 24, 0.2);
                                                position: relative;
                                                overflow: hidden;
                                                font-family: 'Montserrat', sans-serif;
                                                letter-spacing: 0.5px;
                                                text-transform: uppercase;
                                            \" onmouseover=\"this.style.transform='translateY(-3px) scale(1.05)'; this.style.boxShadow='0 6px 20px rgba(169, 4, 24, 0.3)'; this.querySelector('.btn-shimmer').style.left='100%'\"
                                               onmouseout=\"this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 4px 15px rgba(169, 4, 24, 0.2)'; this.querySelector('.btn-shimmer').style.left='-100%'\">
                                                <span style=\"position: relative; z-index: 2;\">
                                                    <i class=\"fas fa-arrow-right me-2\"></i>Learn More
                                                </span>
                                                <div class=\"btn-shimmer\" style=\"position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.6s ease; z-index: 1;\"></div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Enhanced Explore Courses Button -->
                        <div class=\"text-center mt-5 mb-4\">
                            <a href=\"{{ path('app_courses') }}\" class=\"btn btn-lg px-5 py-3\" style=\"
                                border: 2px solid #971020;
                                border-radius: 8px;
                                font-weight: 600;
                                background: #971020;
                                color: white;
                                text-decoration: none;
                                transition: all 0.3s ease;
                                font-family: 'Montserrat', sans-serif;

                            \" onmouseover=\"this.style.background='white'; this.style.color='#971020'; this.style.transform='translateY(-2px)'\"
                               onmouseout=\"this.style.background='#971020'; this.style.color='white'; this.style.transform='translateY(0)'\">
                                <span style=\"position: relative; z-index: 1;\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>Explore Our Courses
                                </span>
                                <div style=\"position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.5s;\"></div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Free Trading Stream -->
    <section class=\"trading-stream-section position-relative\" style=\"
        background-image: url('{{ asset('images/backgrounds/Background 3 Free Trading Stream.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
    \">
        <!-- Overlay -->
        <div class=\"position-absolute top-0 start-0 w-100 h-100\" style=\"background: rgba(0, 0, 0, 0.6); z-index: 1;\"></div>

        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <div class=\"row align-items-center\">
                <!-- Left: Content -->
                <div class=\"col-lg-6 text-white mb-4 mb-lg-0 pe-5\">
                    <h2 class=\"fw-bold mb-4\" style=\"font-size: 2.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); font-family: 'Montserrat', sans-serif;\">Free Trading Stream</h2>
                    <p class=\"mb-0\" style=\"font-size: 1.3rem; line-height: 1.6; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); font-family: 'Calibri', Arial, sans-serif;\">
                        Join our experts for real-time market analysis, detailed trade breakdowns, and expert guidance. From beginners to seasoned traders, our stream provides the knowledge you need to make informed decisions and improve your trading skills.
                    </p>
                </div>

                <!-- Right: YouTube Video Embed - Smaller Size -->
                <div class=\"col-lg-6\">
                    <div class=\"video-embed-container position-relative\" style=\"
                        border-radius: 15px;
                        overflow: hidden;
                        box-shadow: 0 15px 30px rgba(0,0,0,0.4);
                        background: #000;
                        aspect-ratio: 16/9;
                        max-width: 450px;
                        margin: 0 auto;
                    \">
                        <iframe
                            src=\"https://www.youtube.com/embed/3ox5jXJvRpU?rel=0&modestbranding=1&showinfo=0\"
                            title=\"Free Trading Stream\"
                            frameborder=\"0\"
                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"
                            allowfullscreen
                            style=\"
                                width: 100%;
                                height: 100%;
                                border-radius: 15px;
                            \">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- Past Trading Stream Content within same section -->
        <div class=\"container position-relative\" style=\"z-index: 2; margin-top: 4rem;\">
            <div class=\"row\">
                <div class=\"col-12 text-white mb-3\">
                    <h2 class=\"fw-bold mb-2\" style=\"font-size: 2rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); font-family: 'Montserrat', sans-serif;\">Past Trading Stream</h2>
                    <!-- White horizontal line under title -->
                    <div style=\"width: 100%; height: 2px; background: white; margin-bottom: 1.5rem;\"></div>
                </div>
            </div>

            <!-- 4 Video Thumbnails in a Row -->
            <div class=\"row g-4 justify-content-center\">
                {% if past_videos|length > 0 %}
                    {% for video in past_videos|slice(0, 4) %}
                    <div class=\"col-lg-3 col-md-6\">
                        <div class=\"video-card position-relative\" style=\"
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 10px 25px rgba(0,0,0,0.4);
                            transition: all 0.3s ease;
                            cursor: pointer;
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255,255,255,0.2);
                        \" onmouseover=\"this.style.transform='translateY(-8px) scale(1.02)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.6)'\"
                           onmouseout=\"this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.4)'\"
                           onclick=\"openVideoPlayer({{ video.id }})\">
                            <div class=\"video-thumbnail position-relative\" style=\"height: 160px;\">
                                {% if video.thumbnail %}
                                    <img src=\"{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}\"
                                         alt=\"{{ video.title }}\"
                                         class=\"w-100 h-100\"
                                         style=\"object-fit: cover;\"
                                         onerror=\"this.src='{{ asset('images/placeholders/video-placeholder.jpg') }}'\">
                                {% else %}
                                    <div class=\"w-100 h-100\" style=\"background: linear-gradient(135deg, #011a2d 0%, #a90418 100%); display: flex; align-items: center; justify-content: center;\">
                                        <i class=\"fas fa-video text-white\" style=\"font-size: 2rem; opacity: 0.7;\"></i>
                                    </div>
                                {% endif %}

                                <!-- Play overlay -->
                                <div class=\"video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\" style=\"
                                    background: rgba(0,0,0,0.3);
                                    transition: all 0.3s ease;
                                \">
                                    <div class=\"play-button text-center\">
                                        <div class=\"play-icon\" style=\"
                                            width: 50px;
                                            height: 50px;
                                            background: rgba(255,255,255,0.9);
                                            border-radius: 50%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin: 0 auto;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                                        \">
                                            <i class=\"fas fa-play\" style=\"font-size: 1.2rem; margin-left: 2px; color: #011a2d;\"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class=\"video-info p-3 text-white\">
                                <h6 class=\"fw-bold mb-1\" style=\"font-size: 0.9rem; line-height: 1.3;\">{{ video.title|length > 25 ? video.title|slice(0, 25) ~ '...' : video.title }}</h6>
                                <p class=\"small text-white-50 mb-0\">{{ video.category ?? 'Trading Stream' }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    {% for i in 1..4 %}
                    <div class=\"col-lg-3 col-md-6\">
                        <div class=\"video-card position-relative\" style=\"
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 10px 25px rgba(0,0,0,0.4);
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255,255,255,0.2);
                        \">
                            <div class=\"video-thumbnail position-relative\" style=\"height: 160px; background: linear-gradient(135deg, #011a2d 0%, #a90418 100%); display: flex; align-items: center; justify-content: center;\">
                                <i class=\"fas fa-video text-white\" style=\"font-size: 2rem; opacity: 0.7;\"></i>
                            </div>
                            <div class=\"video-info p-3 text-white\">
                                <h6 class=\"fw-bold mb-1\" style=\"font-size: 0.9rem;\">Sample Video {{ i }}</h6>
                                <p class=\"small text-white-50 mb-0\">Trading Stream</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>

            <!-- Explore Videos Button -->
            <div class=\"text-center mt-5\">
                <a href=\"{{ path('app_videos') }}\" class=\"btn btn-outline-light btn-lg px-5 py-3\" style=\"
                    border: 2px solid white;
                    border-radius: 8px;
                    font-weight: 600;
                    background: transparent;
                    color: white;
                    transition: all 0.3s ease;
                    font-family: 'Montserrat', sans-serif;
                    margin-top: 30px;
                    margin-bottom: 20px;
                \" onmouseover=\"this.style.background='white'; this.style.color='#00233e'; this.style.transform='translateY(-2px)'\"
                   onmouseout=\"this.style.background='transparent'; this.style.color='white'; this.style.transform='translateY(0)'\">
                    <i class=\"fas fa-video me-2\"></i>Explore Our Videos
                </a>
            </div>

        </div>
    </section>

    <!-- New Analysis Section -->
    <section class=\"analysis-section position-relative\" style=\"
        background-image: url('{{ asset('images/backgrounds/Background Get your Analysis HP.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
        min-height: 100vh;
    \">
        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <!-- Centered Title with Top Margin -->
            <div class=\"row mb-5\" style=\"margin-top: 2rem;\">
                <div class=\"col-12 text-center\">
                    <h2 class=\"fw-bold\" style=\"
                        font-size: 3rem;
                        color: #00233e;
                        font-family: 'Montserrat', sans-serif;
                    \">Get Your ANALYSIS Today!</h2>
                </div>
            </div>

            <div class=\"row align-items-center\">
                <!-- Left: Content with proper spacing -->
                <div class=\"col-lg-6 mb-4 mb-lg-0 pe-5\">
                    <h4 class=\"fw-bold mb-4\" style=\"
                        color: #971020;
                        font-family: 'Montserrat', sans-serif;
                        font-size: 2.2rem;
                    \">Register on our Analysis</h4>

                    <p class=\"mb-4\" style=\"
                        font-size: 1.4rem;
                        line-height: 1.7;
                        color: #45403f;
                        font-family: 'Calibri', Arial, sans-serif;
                    \">
                        Get personalized financial market analysis from our expert team. Our comprehensive analysis covers market trends, technical indicators, risk assessment, and strategic recommendations tailored to your investment goals. Join thousands of successful traders who rely on our professional insights to make informed trading decisions.
                    </p>
                </div>

                <!-- Right: Email Form with more spacing and smaller container -->
                <div class=\"col-lg-6 ps-5\">
                    <div class=\"d-flex justify-content-center\">
                        <div class=\"analysis-form-container\" style=\"
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 20px;
                            padding: 25px;
                            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
                            backdrop-filter: blur(10px);
                            max-width: 400px;
                            width: 100%;
                        \">
                        <form class=\"analysis-form\">
                            <div class=\"mb-3\">
                                <input type=\"text\" class=\"form-control\" placeholder=\"Full Name\" style=\"
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                \" onfocus=\"this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'\"
                                   onblur=\"this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'\">
                            </div>
                            <div class=\"mb-3\">
                                <input type=\"email\" class=\"form-control\" placeholder=\"Email Address\" style=\"
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                \" onfocus=\"this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'\"
                                   onblur=\"this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'\">
                            </div>
                            <div class=\"mb-3\">
                                <input type=\"tel\" class=\"form-control\" placeholder=\"Phone Number\" style=\"
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                \" onfocus=\"this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'\"
                                   onblur=\"this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'\">
                            </div>
                            <button type=\"submit\" class=\"btn w-100 py-3 fw-bold\" style=\"
                                background: #a90418;
                                color: white;
                                border: none;
                                border-radius: 15px;
                                font-size: 1.1rem;
                                transition: all 0.3s ease;
                                padding: 15px 20px;
                            \" onmouseover=\"this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(169,4,24,0.4)'\"
                               onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='none'\">
                                <i class=\"fas fa-chart-line me-2\"></i>Get Analysis
                            </button>
                        </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- New Trading Made Easy Section -->
    <section class=\"trading-made-easy-section position-relative\" style=\"
        background-image: url('{{ asset('images/backgrounds/Background Trading Made Easy HP.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
        min-height: 100vh;
    \">
        <!-- Overlay -->
        <div class=\"position-absolute top-0 start-0 w-100 h-100\" style=\"background: rgba(0, 0, 0, 0.3); z-index: 1;\"></div>

        <div class=\"container position-relative\" style=\"z-index: 2;\">
            <!-- Centered Container with Top Margin -->
            <div class=\"row justify-content-start\" style=\"margin-top: 3rem;\">
                <div class=\"col-lg-8 col-xl-6\">
                    <div class=\"glassmorphism-container\" style=\"
                        background: rgba(255, 255, 255, 0.08);
                        backdrop-filter: blur(15px);
                        border-radius: 25px;
                        padding: 50px;
                        border: 2px solid rgba(255, 255, 255, 0.15);
                        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                        text-align: left;
                        max-width: 600px;
                        margin: 0;
                    \">
                        <h2 class=\"fw-bold text-white\" style=\"
                            font-size: 2.8rem;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
                            font-family: 'Montserrat', sans-serif;
                            margin-bottom: 2rem;
                        \">Trading Made Easy!</h2>

                        <p class=\"text-white\" style=\"
                            font-size: 1.3rem;
                            line-height: 1.8;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                            font-family: 'Calibri', Arial, sans-serif;
                            margin-bottom: 2rem;
                        \">
                            Learn Technical Analysis at your own pace with professional instructors.
                        </p>

                        <p class=\"text-white\" style=\"
                            font-size: 1.3rem;
                            line-height: 1.8;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                            font-family: 'Calibri', Arial, sans-serif;
                            margin-bottom: 3rem;
                        \">
                            Register now and Discover Financial Freedom
                        </p>

                        <!-- Email Input with Integrated Button -->
                        <div class=\"email-input-container position-relative\" style=\"max-width: 500px;\">
                            <div class=\"input-group position-relative\" style=\"border-radius: 25px; overflow: hidden; box-shadow: 0 8px 25px rgba(0,0,0,0.2);\">
                                <input type=\"email\" class=\"form-control\" placeholder=\"Your Email Address ...\" style=\"
                                    border: none;
                                    padding: 18px 120px 18px 20px;
                                    font-size: 1rem;
                                    background: white;
                                    border-radius: 25px;
                                    width: 100%;
                                \">
                                <a href=\"{{ path('app_contact_registration') }}\" class=\"btn position-absolute\" style=\"
                                    background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 20px;
                                    font-size: 0.9rem;
                                    font-weight: 600;
                                    text-decoration: none;
                                    border-radius: 20px;
                                    transition: all 0.3s ease;
                                    display: flex;
                                    align-items: center;
                                    right: 6px;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    z-index: 10;
                                \" onmouseover=\"this.style.background='linear-gradient(135deg, #8b0314 0%, #6d0210 100%)'\"
                                   onmouseout=\"this.style.background='linear-gradient(135deg, #a90418 0%, #8b0314 100%)'\">
                                    <i class=\"fas fa-rocket me-2\"></i>Start Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced seamless partner carousel animation */
@keyframes scroll-partners-seamless {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.partners-carousel {
    animation: scroll-partners-seamless 20s linear infinite;
    white-space: nowrap;
    will-change: transform;
    display: flex;
    align-items: center;
}

.partners-carousel-container {
    mask: linear-gradient(90deg, transparent, white 10%, white 90%, transparent);
    -webkit-mask: linear-gradient(90deg, transparent, white 10%, white 90%, transparent);
}

.partner-set {
    flex-shrink: 0;
}

.partner-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.partner-img {
    max-width: 100%;
    height: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects for partner logos */
.partner-logo:hover .partner-img {
    filter: grayscale(0%) brightness(1) !important;
    transform: scale(1.1) !important;
    opacity: 1 !important;
}

/* Responsive partner carousel */
@media (max-width: 768px) {
    .partner-logo {
        min-width: 100px !important;
        margin: 0 0.75rem !important;
    }

    .partner-img {
        height: 35px !important;
    }

    .partners-carousel {
        animation-duration: 15s !important;
    }
}

/* Floating animation for background elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Hero section responsive adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.1rem !important;
    }

    .stock-ticker .ticker-content {
        font-size: 0.8rem;
    }

    .countdown-timer {
        font-size: 0.8rem;
    }
}

/* Course cards hover effects */
.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.4) !important;
}

/* Video cards hover effects */
.video-card:hover .play-icon {
    transform: scale(1.1);
    background: rgba(255,255,255,0.3) !important;
}

/* Form focus effects */
.form-control:focus, .form-select:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Background parallax effect */
.hero-section,
.courses-section,
.trading-stream-section,
.past-streams-section,
.signup-section {
    background-attachment: fixed;
}

@media (max-width: 768px) {
    .hero-section,
    .courses-section,
    .trading-stream-section,
    .past-streams-section,
    .signup-section {
        background-attachment: scroll;
    }
}

/* Loading animation for images */
img {
    transition: opacity 0.3s ease;
}

/* Ensure footer social media icons are circular on homepage */
footer .footer-social-btn {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid #a90418 !important;
    color: #a90418 !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
}

footer .footer-social-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3) !important;
    background: #a90418 !important;
    border-color: #a90418 !important;
    color: white !important;
}

/* Gradient text animation */
@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Shimmer animation for course cards */
@keyframes shimmer {
    0% {
        transform: rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: rotate(180deg);
        opacity: 0.7;
    }
    100% {
        transform: rotate(360deg);
        opacity: 0.3;
    }
}

.gradient-text {
    background: linear-gradient(-45deg, #00ff00, #ff0000, #00ff00, #ff0000);
    background-size: 400% 400%;
    animation: gradient-shift 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Our Courses Section Responsive Styles */
@media (max-width: 768px) {
    .courses-section h2 {
        font-size: 2.5rem !important;
    }

    .courses-section h3 {
        font-size: 1.5rem !important;
    }

    .courses-section p {
        font-size: 1rem !important;
    }

    .course-card {
        width: 260px !important;
        height: 300px !important;
        margin-bottom: 1.2rem !important;
    }

    .certificate-container {
        margin-top: 2rem;
        transform: rotate(0deg) !important;
    }

    .certificate-container:hover {
        transform: rotate(0deg) scale(1.02) !important;
    }

    .course-card {
        margin-bottom: 2rem !important;
        width: 320px !important;
        height: 320px !important;
    }

    .course-header {
        min-height: 45px !important;
        padding: 0.6rem !important;
    }

    .course-header h5 {
        font-size: 0.8rem !important;
    }

    .course-thumbnail {
        padding: 0.8rem !important;
    }

    .course-thumbnail img,
    .course-thumbnail div {
        height: 140px !important;
    }

    .course-footer {
        padding: 0.8rem !important;
    }

    .course-footer .row .col-6 div {
        font-size: 0.75rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    .course-footer .row .col-6 div i {
        font-size: 0.7rem !important;
    }
}

@media (max-width: 576px) {
    .courses-section h2 {
        font-size: 2rem !important;
    }

    .courses-section h3 {
        font-size: 1.3rem !important;
    }

    .course-card {
        width: 240px !important;
        height: 280px !important;
        margin-bottom: 1rem !important;
    }

    .course-thumbnail img,
    .course-thumbnail div {
        height: 120px !important;
    }

    .course-header {
        min-height: 40px !important;
        padding: 0.5rem !important;
    }

    .course-header h5 {
        font-size: 0.75rem !important;
    }

    .course-footer {
        padding: 0.8rem !important;
    }

    .course-footer .row .col-6 div {
        font-size: 0.7rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    .course-footer .row .col-6 div i {
        font-size: 0.65rem !important;
        margin-bottom: 0.1rem !important;
    }

    .course-footer .row .col-6 div div {
        font-size: 0.65rem !important;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Homepage specific functionality - countdown timer removed to avoid conflicts with promotional banner

// Enhanced seamless partner carousel
document.addEventListener('DOMContentLoaded', function() {
    const partnersCarousel = document.getElementById('partnersCarousel');

    if (partnersCarousel) {
        // Ensure seamless scrolling by monitoring animation
        partnersCarousel.addEventListener('animationiteration', function() {
            // This event fires each time the animation completes a cycle
            // The CSS animation automatically resets to 0, creating seamless loop
        });

        // Continuous scrolling - no pause on hover

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            partnersCarousel.style.animation = 'none';
            partnersCarousel.style.transform = 'translateX(0)';
        }
    }
});

// Form submission handlers
document.addEventListener('DOMContentLoaded', function() {
    // Analysis form submission
    const analysisForm = document.querySelector('.analysis-form');
    if (analysisForm) {
        analysisForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type=\"submit\"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Processing...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class=\"fas fa-check me-2\"></i>Success!';
                submitBtn.style.background = '#28a745';

                // Reset form
                setTimeout(() => {
                    this.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = '#a90418';
                    submitBtn.disabled = false;
                }, 2000);
            }, 1500);
        });
    }

    // Newsletter form submission (if any remaining)
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type=\"submit\"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Processing...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class=\"fas fa-check me-2\"></i>Success!';
                submitBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

                // Reset form
                setTimeout(() => {
                    this.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = 'linear-gradient(135deg, #a90418 0%, #011a2d 100%)';
                    submitBtn.disabled = false;
                }, 2000);
            }, 1500);
        });
    }
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animatedElements = document.querySelectorAll('.course-card, .video-card, .benefit-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Video play functionality
function playVideo(container) {
    const overlay = container.querySelector('.video-overlay');
    if (overlay) {
        overlay.innerHTML = '<div class=\"text-center\"><i class=\"fas fa-spinner fa-spin text-white\" style=\"font-size: 2rem;\"></i><p class=\"text-white mt-2\">Loading video...</p></div>';

        // Simulate loading
        setTimeout(() => {
            overlay.innerHTML = '<div class=\"text-center\"><i class=\"fas fa-play text-white\" style=\"font-size: 2rem;\"></i><p class=\"text-white mt-2\">Video would play here</p></div>';
        }, 1500);
    }
}

// Open video player in new tab/window
function openVideoPlayer(videoId) {
    if (videoId) {
        // Open video in new tab
        window.open('/videos/' + videoId, '_blank');
    } else {
        // Fallback for demo videos
        alert('Video player would open here. This is a demo video.');
    }
}

// Video play button interactions
document.querySelectorAll('.play-button, .play-icon').forEach(button => {
    button.addEventListener('click', function(e) {
        e.stopPropagation();
        playVideo(this.closest('.video-container, .video-card'));
    });
});

// Dynamic stats animation
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (stat.textContent.includes('+') || stat.textContent.includes('%')) {
            const originalText = stat.textContent;
            let counter = 0;
            const target = parseFloat(originalText.replace(/[^\\d.-]/g, ''));
            const increment = target / 50;

            const timer = setInterval(() => {
                counter += increment;
                if (counter >= target) {
                    stat.textContent = originalText;
                    clearInterval(timer);
                } else {
                    stat.textContent = originalText.replace(target.toString(), Math.floor(counter).toString());
                }
            }, 50);
        }
    });
}

// Trigger stats animation when section is visible
const statsSection = document.querySelector('.trading-stream-section');
if (statsSection) {
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStats();
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statsObserver.observe(statsSection);
}

// Course Cards Animation
document.addEventListener('DOMContentLoaded', function() {
    // Animate course cards on scroll
    const courseCards = document.querySelectorAll('.course-card');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                cardObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    courseCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });
});
</script>
{% endblock %}", "home/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\home\\index.html.twig");
    }
}
