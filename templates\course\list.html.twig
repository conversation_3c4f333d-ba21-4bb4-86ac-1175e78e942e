{% extends 'base.html.twig' %}

{% block title %}Professional Trading Courses - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Calibri:wght@400;500;600&display=swap" rel="stylesheet">
{% endblock %}

{% block body %}
<div class="container-fluid px-0">
    <!-- Hero Section -->
    <section class="hero-section position-relative" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); min-height: 70vh; display: flex; align-items: center;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="hero-content text-white">
                        <h1 class="display-3 fw-bold mb-4" style="font-family: 'Montserrat', sans-serif; line-height: 1.2;">
                            Professional Trading Courses
                        </h1>
                        <p class="lead mb-4" style="font-family: 'Calibri', sans-serif; font-size: 1.25rem; opacity: 0.9;">
                            Master the financial markets with our comprehensive course catalog. Expert-led programs designed to take you from beginner to professional trader.
                        </p>
                        <div class="course-stats d-flex flex-wrap gap-4 mt-4">
                            <div class="stat-item text-center">
                                <div class="stat-number h2 fw-bold mb-1" style="color: #a90418;">{{ courses|length > 0 ? courses|length : '12+' }}</div>
                                <div class="stat-label small text-white-50">Expert Courses</div>
                            </div>
                            <div class="stat-item text-center">
                                <div class="stat-number h2 fw-bold mb-1" style="color: #a90418;">5000+</div>
                                <div class="stat-label small text-white-50">Students Trained</div>
                            </div>
                            <div class="stat-item text-center">
                                <div class="stat-number h2 fw-bold mb-1" style="color: #a90418;">95%</div>
                                <div class="stat-label small text-white-50">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="hero-image">
                        <i class="fas fa-chart-line" style="font-size: 8rem; color: rgba(169, 4, 24, 0.3);"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="py-5" style="background-color: #F6F7F9;">
        <div class="container">
            <!-- Section Header -->
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold mb-3" style="font-family: 'Montserrat', sans-serif; color: #011a2d;">
                        Our Course Catalog
                    </h2>
                    <p class="lead text-muted" style="font-family: 'Calibri', sans-serif;">
                        Choose from our comprehensive selection of trading and financial education courses
                    </p>
                </div>
            </div>

            <!-- Courses Grid -->
            {% if courses and courses|length > 0 %}
                <div class="row g-4">
                    {% for course in courses %}
                        <div class="col-lg-4 col-md-6">
                            <div class="course-card h-100 border-0 shadow-sm" style="border-radius: 12px; overflow: hidden; transition: all 0.3s ease; background: white;">
                                <!-- Course Image -->
                                <div class="course-image-container position-relative" style="height: 220px; overflow: hidden;">
                                    {% if course.thumbnailImage %}
                                        <img src="{{ asset(course.thumbnailImage) }}" alt="{{ course.title }}" class="w-100 h-100" style="object-fit: cover; transition: transform 0.3s ease;">
                                    {% else %}
                                        <div class="course-placeholder d-flex align-items-center justify-content-center h-100" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);">
                                            <div class="text-center text-white">
                                                <i class="fas fa-chart-line fa-3x mb-2" style="opacity: 0.7;"></i>
                                                <div class="fw-bold" style="font-family: 'Montserrat', sans-serif;">{{ course.code }}</div>
                                            </div>
                                        </div>
                                    {% endif %}
                                    
                                    <!-- Course Badge -->
                                    <div class="position-absolute top-0 start-0 m-3">
                                        <span class="badge px-3 py-2" style="background-color: #a90418; font-family: 'Montserrat', sans-serif; font-weight: 600;">
                                            {{ course.code }}
                                        </span>
                                    </div>
                                    
                                    <!-- Level Badge -->
                                    <div class="position-absolute top-0 end-0 m-3">
                                        <span class="badge px-3 py-2" style="background-color: rgba(1, 26, 45, 0.9); font-family: 'Montserrat', sans-serif;">
                                            {{ course.level|default('Beginner') }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Course Content -->
                                <div class="card-body p-4 d-flex flex-column">
                                    <!-- Course Title -->
                                    <h5 class="card-title fw-bold mb-3" style="font-family: 'Montserrat', sans-serif; color: #011a2d; line-height: 1.3;">
                                        {{ course.title }}
                                    </h5>
                                    
                                    <!-- Course Description -->
                                    <p class="card-text text-muted mb-3 flex-grow-1" style="font-family: 'Calibri', sans-serif; line-height: 1.6;">
                                        {{ course.description|length > 120 ? course.description|slice(0, 120) ~ '...' : course.description }}
                                    </p>
                                    
                                    <!-- Course Meta -->
                                    <div class="course-meta mb-3">
                                        <div class="d-flex align-items-center justify-content-between text-muted small">
                                            <span style="font-family: 'Calibri', sans-serif;">
                                                <i class="fas fa-tag me-1" style="color: #a90418;"></i>
                                                {{ course.category }}
                                            </span>
                                            <span style="font-family: 'Calibri', sans-serif;">
                                                <i class="fas fa-eye me-1" style="color: #a90418;"></i>
                                                {{ course.viewCount }} views
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <!-- Course Actions -->
                                    <div class="course-actions mt-auto">
                                        <div class="d-grid gap-2">
                                            <a href="{{ path('app_course_show', {code: course.code}) }}" 
                                               class="btn btn-primary fw-semibold" 
                                               style="background-color: #011a2d; border-color: #011a2d; font-family: 'Montserrat', sans-serif; transition: all 0.3s ease;">
                                                <i class="fas fa-eye me-2"></i>View Course Details
                                            </a>
                                            <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" 
                                               class="btn btn-outline-danger fw-semibold" 
                                               style="border-color: #a90418; color: #a90418; font-family: 'Montserrat', sans-serif; transition: all 0.3s ease;">
                                                <i class="fas fa-envelope me-2"></i>Contact for Enrollment
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- No Courses Available -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-graduation-cap" style="font-size: 5rem; color: #a90418; opacity: 0.3;"></i>
                    </div>
                    <h3 class="fw-bold mb-3" style="font-family: 'Montserrat', sans-serif; color: #011a2d;">
                        Courses Coming Soon
                    </h3>
                    <p class="lead text-muted mb-4" style="font-family: 'Calibri', sans-serif;">
                        We're preparing our comprehensive course catalog. Check back soon for exciting new trading courses.
                    </p>
                    <a href="{{ path('app_contact') }}" class="btn btn-primary btn-lg" style="background-color: #a90418; border-color: #a90418; font-family: 'Montserrat', sans-serif;">
                        <i class="fas fa-envelope me-2"></i>Get Notified
                    </a>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-5" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold text-white mb-3" style="font-family: 'Montserrat', sans-serif;">
                        Ready to Start Your Trading Journey?
                    </h3>
                    <p class="text-white-50 mb-0" style="font-family: 'Calibri', sans-serif;">
                        Join thousands of successful traders who have transformed their financial future with Capitol Academy.
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="{{ path('app_contact') }}" class="btn btn-lg fw-semibold" style="background-color: #a90418; border-color: #a90418; color: white; font-family: 'Montserrat', sans-serif;">
                        <i class="fas fa-rocket me-2"></i>Get Started Today
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Course Card Hover Effects */
.course-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(1, 26, 45, 0.15) !important;
}

.course-card:hover .course-image-container img {
    transform: scale(1.05);
}

/* Button Hover Effects */
.btn-primary:hover {
    background-color: #a90418 !important;
    border-color: #a90418 !important;
    transform: translateY(-1px);
}

.btn-outline-danger:hover {
    background-color: #a90418 !important;
    border-color: #a90418 !important;
    color: white !important;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh !important;
    }

    .display-3 {
        font-size: 2.5rem !important;
    }

    .course-stats {
        justify-content: center !important;
    }

    .stat-item {
        min-width: 80px;
    }
}

/* Accessibility */
.course-card:focus-within {
    outline: 2px solid #a90418;
    outline-offset: 2px;
}

/* Animation */
.course-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional styling enhancements */
.course-card {
    border: 1px solid rgba(1, 26, 45, 0.1);
}

.badge {
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.btn {
    border-radius: 6px;
    font-weight: 600;
    letter-spacing: 0.3px;
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}
</style>
{% endblock %}
