<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add missing fields to onsite_course table for new onsite course creation functionality
 */
final class Version20250719000005 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add banner_image, duration, and price fields to onsite_course table';
    }

    public function up(Schema $schema): void
    {
        // Check if columns exist before adding them
        $this->addSql('ALTER TABLE onsite_course 
            ADD COLUMN IF NOT EXISTS banner_image VARCHAR(255) DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS duration INT DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS price DECIMAL(10,2) DEFAULT "0.00"');
    }

    public function down(Schema $schema): void
    {
        // Remove the added columns
        $this->addSql('ALTER TABLE onsite_course 
            DROP COLUMN IF EXISTS banner_image,
            DROP COLUMN IF EXISTS duration,
            DROP COLUMN IF EXISTS price');
    }
}
