<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAdminRemoteCourseControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\AdminRemoteCourseController' shared autowired service.
     *
     * @return \App\Controller\AdminRemoteCourseController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AdminRemoteCourseController.php';

        $container->services['App\\Controller\\AdminRemoteCourseController'] = $instance = new \App\Controller\AdminRemoteCourseController(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Service\\AdminPermissionService'] ?? $container->load('getAdminPermissionServiceService')), ($container->privates['App\\Repository\\RemoteCourseRepository'] ?? $container->load('getRemoteCourseRepositoryService')), ($container->privates['App\\Repository\\RemoteCourseChapterRepository'] ?? $container->load('getRemoteCourseChapterRepositoryService')), ($container->privates['App\\Repository\\RemoteCourseVideoRepository'] ?? $container->load('getRemoteCourseVideoRepositoryService')), ($container->privates['App\\Repository\\VideoRepository'] ?? $container->load('getVideoRepositoryService')));

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\AdminRemoteCourseController', $container));

        return $instance;
    }
}
