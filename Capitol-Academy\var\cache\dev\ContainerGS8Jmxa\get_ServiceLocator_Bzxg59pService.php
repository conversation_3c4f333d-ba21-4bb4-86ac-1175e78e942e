<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_Bzxg59pService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.bzxg59p' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.bzxg59p'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseVideo' => ['privates', '.errored..service_locator.bzxg59p.App\\Entity\\RemoteCourseVideo', NULL, 'Cannot autowire service ".service_locator.bzxg59p": it needs an instance of "App\\Entity\\RemoteCourseVideo" but this type has been excluded in "config/services.yaml".'],
        ], [
            'courseVideo' => 'App\\Entity\\RemoteCourseVideo',
        ]);
    }
}
