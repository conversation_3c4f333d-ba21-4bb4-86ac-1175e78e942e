<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerGS8Jmxa\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerGS8Jmxa/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerGS8Jmxa.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerGS8Jmxa\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerGS8Jmxa\App_KernelDevDebugContainer([
    'container.build_hash' => 'GS8Jmxa',
    'container.build_id' => '715eb274',
    'container.build_time' => 1752940906,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerGS8Jmxa');
