<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_B22XE3CService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.B22XE3C' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.B22XE3C'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'course' => ['privates', '.errored..service_locator.B22XE3C.App\\Entity\\OnsiteCourse', NULL, 'Cannot autowire service ".service_locator.B22XE3C": it needs an instance of "App\\Entity\\OnsiteCourse" but this type has been excluded in "config/services.yaml".'],
        ], [
            'course' => 'App\\Entity\\OnsiteCourse',
        ]);
    }
}
