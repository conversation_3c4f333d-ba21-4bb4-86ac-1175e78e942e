<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_J_WvTYMService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.j.WvTYM' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.j.WvTYM'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', 'App\\Repository\\OnsiteCourseRepository', 'getOnsiteCourseRepositoryService', true],
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'moduleRepository' => ['privates', 'App\\Repository\\OnsiteCourseModuleRepository', 'getOnsiteCourseModuleRepositoryService', true],
        ], [
            'courseRepository' => 'App\\Repository\\OnsiteCourseRepository',
            'entityManager' => '?',
            'moduleRepository' => 'App\\Repository\\OnsiteCourseModuleRepository',
        ]);
    }
}
