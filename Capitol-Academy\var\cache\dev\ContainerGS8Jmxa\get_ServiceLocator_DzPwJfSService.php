<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_DzPwJfSService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.DzPwJfS' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.DzPwJfS'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', '.errored.OcgVrjP', NULL, 'Cannot determine controller argument for "App\\Controller\\PaymentController::redirectToContact()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Repository\\CourseRepository".'],
        ], [
            'courseRepository' => '?',
        ]);
    }
}
