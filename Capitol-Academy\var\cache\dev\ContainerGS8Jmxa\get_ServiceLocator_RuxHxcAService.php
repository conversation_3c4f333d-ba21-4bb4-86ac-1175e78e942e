<?php

namespace ContainerGS8Jmxa;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_RuxHxcAService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.ruxHxcA' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.ruxHxcA'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'order' => ['privates', '.errored..service_locator.ruxHxcA.App\\Entity\\Order', NULL, 'Cannot autowire service ".service_locator.ruxHxcA": it needs an instance of "App\\Entity\\Order" but this type has been excluded in "config/services.yaml".'],
        ], [
            'order' => 'App\\Entity\\Order',
        ]);
    }
}
