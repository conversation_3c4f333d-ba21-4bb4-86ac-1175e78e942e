<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/edit.html.twig */
class __TwigTemplate_9a97b85801d75e4a035de3db29911f67 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Onsite Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Onsite Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Edit ";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Onsite Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 30
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0 me-2\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href=\"";
        // line 39
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 39, $this->source); })()), "code", [], "any", false, false, false, 39)]), "html", null, true);
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#138496';\"
                           onmouseout=\"this.style.background='#17a2b8';\">
                            <i class=\"fas fa-eye me-2\"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 53
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("onsite_course_edit"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"";
        // line 54
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 54, $this->source); })()), "isActive", [], "any", false, false, false, 54)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("1") : ("0"));
        yield "\">
            <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Code <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"code\"
                                           name=\"code\"
                                           value=\"";
        // line 72
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 72, $this->source); })()), "code", [], "any", false, false, false, 72), "html", null, true);
        yield "\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Title <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"title\"
                                           name=\"title\"
                                           value=\"";
        // line 94
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 94, $this->source); })()), "title", [], "any", false, false, false, 94), "html", null, true);
        yield "\"
                                           placeholder=\"Enter course title\"
                                           required
                                           maxlength=\"255\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <!-- Category -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Category
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"category\"
                                            name=\"category\"
                                            aria-describedby=\"category_help category_error\"
                                            aria-label=\"Select a course category\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a category...</option>
                                        ";
        // line 122
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 122, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 123
            yield "                                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 123), "html", null, true);
            yield "\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 123, $this->source); })()), "category", [], "any", false, false, false, 123) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 123))) ? ("selected") : (""));
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 123), "html", null, true);
            yield "</option>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 125
        yield "                                    </select>
                                    <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the category that best describes this course. Use arrow keys to navigate options.
                                    </div>
                                    <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Level
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"level\"
                                            name=\"level\"
                                            aria-describedby=\"level_help level_error\"
                                            aria-label=\"Select course level\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a level...</option>
                                        ";
        // line 149
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["levels"]) || array_key_exists("levels", $context) ? $context["levels"] : (function () { throw new RuntimeError('Variable "levels" does not exist.', 149, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["level"]) {
            // line 150
            yield "                                            <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["level"], "html", null, true);
            yield "\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 150, $this->source); })()), "level", [], "any", false, false, false, 150) == $context["level"])) ? ("selected") : (""));
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["level"], "html", null, true);
            yield "</option>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['level'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 152
        yield "                                        ";
        if (Twig\Extension\CoreExtension::testEmpty((isset($context["levels"]) || array_key_exists("levels", $context) ? $context["levels"] : (function () { throw new RuntimeError('Variable "levels" does not exist.', 152, $this->source); })()))) {
            // line 153
            yield "                                            <option value=\"Beginner\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 153, $this->source); })()), "level", [], "any", false, false, false, 153) == "Beginner")) ? ("selected") : (""));
            yield ">Beginner</option>
                                            <option value=\"Intermediate\" ";
            // line 154
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 154, $this->source); })()), "level", [], "any", false, false, false, 154) == "Intermediate")) ? ("selected") : (""));
            yield ">Intermediate</option>
                                            <option value=\"Advanced\" ";
            // line 155
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 155, $this->source); })()), "level", [], "any", false, false, false, 155) == "Advanced")) ? ("selected") : (""));
            yield ">Advanced</option>
                                            <option value=\"Expert\" ";
            // line 156
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 156, $this->source); })()), "level", [], "any", false, false, false, 156) == "Expert")) ? ("selected") : (""));
            yield ">Expert</option>
                                        ";
        }
        // line 158
        yield "                                    </select>
                                    <div id=\"level_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the difficulty level of this course.
                                    </div>
                                    <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a level.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Description <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <textarea class=\"form-control enhanced-field\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Provide a detailed description of the course content, objectives, and what students will learn...\"
                                      required
                                      style=\"font-size: 1rem; border: 2px solid #ced4da; resize: vertical;\">";
        // line 181
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 181, $this->source); })()), "description", [], "any", false, false, false, 181), "html", null, true);
        yield "</textarea>
                            <div class=\"invalid-feedback\">
                                Please provide a course description.
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-bullseye text-primary mr-1\" aria-hidden=\"true\"></i>
                                Learning Outcomes <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"learning-outcomes-container\">
                                ";
        // line 194
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 194, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 194)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 195
            yield "                                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 195, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 195));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 196
                yield "                                        <div class=\"input-group mb-2 learning-outcome-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   value=\"";
                // line 200
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "\"
                                                   placeholder=\"Enter a learning outcome...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                ";
                // line 205
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 205)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 206
                    yield "                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                } else {
                    // line 210
                    yield "                                                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                }
                // line 217
                yield "                                            </div>
                                        </div>
                                    ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 220
            yield "                                ";
        } else {
            // line 221
            yield "                                    <div class=\"input-group mb-2 learning-outcome-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master advanced chart analysis techniques\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                ";
        }
        // line 235
        yield "                            </div>
                            <small class=\"form-text text-muted\">Add specific learning outcomes for this course. Click + to add more.</small>
                        </div>

                        <!-- Features -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-star text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Features <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"features-container\">
                                ";
        // line 246
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 246, $this->source); })()), "features", [], "any", false, false, false, 246)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 247
            yield "                                    ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 247, $this->source); })()), "features", [], "any", false, false, false, 247));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 248
                yield "                                        <div class=\"input-group mb-2 feature-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   value=\"";
                // line 252
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "\"
                                                   placeholder=\"Enter a feature...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                ";
                // line 257
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 257)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 258
                    yield "                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                } else {
                    // line 262
                    yield "                                                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                ";
                }
                // line 269
                yield "                                            </div>
                                        </div>
                                    ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 272
            yield "                                ";
        } else {
            // line 273
            yield "                                    <div class=\"input-group mb-2 feature-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Live instructor sessions, Hands-on exercises, Certificate of completion\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                ";
        }
        // line 287
        yield "                            </div>
                            <small class=\"form-text text-muted\">Add key features and benefits of this course. Click + to add more.</small>
                        </div>

                        <!-- Thumbnail Upload -->
                        <div class=\"form-group\">
                            <label for=\"thumbnail_image\" class=\"form-label\">
                                <i class=\"fas fa-image text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Thumbnail
                            </label>
                            <input type=\"file\"
                                   class=\"form-control enhanced-file-field\"
                                   id=\"thumbnail_image\"
                                   name=\"thumbnail_image\"
                                   accept=\"image/*\"
                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                            <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px. Leave empty to keep current thumbnail.
                            </small>

                            <!-- Current Thumbnail Display -->
                            ";
        // line 308
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 308, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 308)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 309
            yield "                            <div class=\"current-thumbnail mt-3 d-flex flex-column align-items-center\">
                                <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                    <img src=\"";
            // line 311
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 311, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 311), "html", null, true);
            yield "\" alt=\"Current Thumbnail\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                </div>
                                <small class=\"form-text text-info d-block mt-2 text-center\">Current Thumbnail (300x200px)</small>
                            </div>
                            ";
        }
        // line 316
        yield "                        </div>

                        <!-- Course has modules toggle -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-puzzle-piece text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Structure
                            </label>
                            <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-top: 0.5rem;\">
                                <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" ";
        // line 325
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 325, $this->source); })()), "hasModules", [], "any", false, false, false, 325)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("checked") : (""));
        yield " style=\"margin-left: 0;\">
                                <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem;\">
                                    Course has modules
                                </label>
                            </div>
                            <small class=\"form-text text-muted\">Enable this if you want to add modules to this course.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"card-footer\" style=\"background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;\">
                            <i class=\"fas fa-save mr-2\"></i>
                            Update Onsite Course
                        </button>
                    </div>
                    <div class=\"col-md-6 text-right\">
                        <a href=\"";
        // line 345
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\" class=\"btn btn-secondary btn-lg\" style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;\">
                            <i class=\"fas fa-times mr-2\"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 357
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 358
        yield "<script>
\$(document).ready(function() {
    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove learning outcome
    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Live instructor sessions, Downloadable resources...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove feature
    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Thumbnail image preview
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Onsite Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Toggle module section visibility (if modules section exists)
    \$('#has_modules').on('change', function() {
        const modulesSection = \$('#modules-section');
        if (modulesSection.length) {
            if (\$(this).is(':checked')) {
                modulesSection.slideDown();
            } else {
                modulesSection.slideUp();
            }
        }
    });

    // Thumbnail preview functionality
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const currentThumbnailContainer = document.querySelector('.current-thumbnail');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update the current thumbnail display with new file
                    if (currentThumbnailContainer) {
                        const imgElement = currentThumbnailContainer.querySelector('img');
                        if (imgElement) {
                            imgElement.src = e.target.result;
                        }

                        // Update the label to show it's a new thumbnail
                        const label = currentThumbnailContainer.querySelector('small');
                        if (label) {
                            label.innerHTML = `New Thumbnail Selected: \${file.name}`;
                            label.className = 'form-text text-success d-block mt-2 text-center';
                        }
                    } else {
                        // Create new thumbnail preview if no current thumbnail exists
                        const thumbnailContainer = document.createElement('div');
                        thumbnailContainer.className = 'current-thumbnail mt-3 d-flex flex-column align-items-center';
                        thumbnailContainer.innerHTML = `
                            <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                <img src=\"\${e.target.result}\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                            </div>
                            <small class=\"form-text text-success d-block mt-2 text-center\">New Thumbnail Selected: \${file.name}</small>
                        `;
                        thumbnailInput.parentNode.appendChild(thumbnailContainer);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        \$(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        \$(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<style>
/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 1rem !important;
    padding-top: 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1rem !important;
}

.enhanced-dropdown option:first-child {
    color: #6c757d;
    font-style: italic;
    font-weight: 400;
}

.enhanced-dropdown option:not(:first-child) {
    color: #011a2d;
    font-weight: 500;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  702 => 358,  689 => 357,  667 => 345,  644 => 325,  633 => 316,  625 => 311,  621 => 309,  619 => 308,  596 => 287,  580 => 273,  577 => 272,  561 => 269,  552 => 262,  546 => 258,  544 => 257,  536 => 252,  530 => 248,  512 => 247,  510 => 246,  497 => 235,  481 => 221,  478 => 220,  462 => 217,  453 => 210,  447 => 206,  445 => 205,  437 => 200,  431 => 196,  413 => 195,  411 => 194,  395 => 181,  370 => 158,  365 => 156,  361 => 155,  357 => 154,  352 => 153,  349 => 152,  336 => 150,  332 => 149,  306 => 125,  293 => 123,  289 => 122,  258 => 94,  233 => 72,  212 => 54,  208 => 53,  191 => 39,  179 => 30,  161 => 14,  148 => 13,  135 => 10,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Edit {{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Onsite Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0 me-2\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href=\"{{ path('admin_onsite_course_preview', {'code': course.code}) }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#138496';\"
                           onmouseout=\"this.style.background='#17a2b8';\">
                            <i class=\"fas fa-eye me-2\"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('onsite_course_edit') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"{{ course.isActive ? '1' : '0' }}\">
            <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">
                        <!-- Course Code and Title Row -->
                        <div class=\"row\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"code\" class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Code <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"code\"
                                           name=\"code\"
                                           value=\"{{ course.code }}\"
                                           placeholder=\"e.g., OSC001, TRAD101\"
                                           required
                                           maxlength=\"10\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"title\" class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Course Title <span class=\"text-danger\" aria-label=\"required\">*</span>
                                    </label>
                                    <input type=\"text\"
                                           class=\"form-control enhanced-field\"
                                           id=\"title\"
                                           name=\"title\"
                                           value=\"{{ course.title }}\"
                                           placeholder=\"Enter course title\"
                                           required
                                           maxlength=\"255\"
                                           style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                    <div class=\"invalid-feedback\">
                                        Please provide a course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row\">
                            <!-- Category -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"category\" class=\"form-label\">
                                        <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Category
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"category\"
                                            name=\"category\"
                                            aria-describedby=\"category_help category_error\"
                                            aria-label=\"Select a course category\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a category...</option>
                                        {% for category in categories %}
                                            <option value=\"{{ category.name }}\" {{ course.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the category that best describes this course. Use arrow keys to navigate options.
                                    </div>
                                    <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label for=\"level\" class=\"form-label\">
                                        <i class=\"fas fa-layer-group text-primary mr-1\" aria-hidden=\"true\"></i>
                                        Level
                                    </label>
                                    <select class=\"form-select enhanced-dropdown\"
                                            id=\"level\"
                                            name=\"level\"
                                            aria-describedby=\"level_help level_error\"
                                            aria-label=\"Select course level\"
                                            style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                        <option value=\"\">Choose a level...</option>
                                        {% for level in levels %}
                                            <option value=\"{{ level }}\" {{ course.level == level ? 'selected' : '' }}>{{ level }}</option>
                                        {% endfor %}
                                        {% if levels is empty %}
                                            <option value=\"Beginner\" {{ course.level == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                            <option value=\"Intermediate\" {{ course.level == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                            <option value=\"Advanced\" {{ course.level == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                            <option value=\"Expert\" {{ course.level == 'Expert' ? 'selected' : '' }}>Expert</option>
                                        {% endif %}
                                    </select>
                                    <div id=\"level_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                        Select the difficulty level of this course.
                                    </div>
                                    <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                        Please select a level.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class=\"form-group\">
                            <label for=\"description\" class=\"form-label\">
                                <i class=\"fas fa-align-left text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Description <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <textarea class=\"form-control enhanced-field\"
                                      id=\"description\"
                                      name=\"description\"
                                      rows=\"4\"
                                      placeholder=\"Provide a detailed description of the course content, objectives, and what students will learn...\"
                                      required
                                      style=\"font-size: 1rem; border: 2px solid #ced4da; resize: vertical;\">{{ course.description }}</textarea>
                            <div class=\"invalid-feedback\">
                                Please provide a course description.
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-bullseye text-primary mr-1\" aria-hidden=\"true\"></i>
                                Learning Outcomes <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"learning-outcomes-container\">
                                {% if course.learningOutcomes %}
                                    {% for outcome in course.learningOutcomes %}
                                        <div class=\"input-group mb-2 learning-outcome-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   value=\"{{ outcome }}\"
                                                   placeholder=\"Enter a learning outcome...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% else %}
                                                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class=\"input-group mb-2 learning-outcome-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master advanced chart analysis techniques\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <small class=\"form-text text-muted\">Add specific learning outcomes for this course. Click + to add more.</small>
                        </div>

                        <!-- Features -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-star text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Features <span class=\"text-danger\" aria-label=\"required\">*</span>
                            </label>
                            <div id=\"features-container\">
                                {% if course.features %}
                                    {% for feature in course.features %}
                                        <div class=\"input-group mb-2 feature-item\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   value=\"{{ feature }}\"
                                                   placeholder=\"Enter a feature...\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% else %}
                                                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                        <i class=\"fas fa-minus\"></i>
                                                    </button>
                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                        <i class=\"fas fa-plus\"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class=\"input-group mb-2 feature-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Live instructor sessions, Hands-on exercises, Certificate of completion\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <small class=\"form-text text-muted\">Add key features and benefits of this course. Click + to add more.</small>
                        </div>

                        <!-- Thumbnail Upload -->
                        <div class=\"form-group\">
                            <label for=\"thumbnail_image\" class=\"form-label\">
                                <i class=\"fas fa-image text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Thumbnail
                            </label>
                            <input type=\"file\"
                                   class=\"form-control enhanced-file-field\"
                                   id=\"thumbnail_image\"
                                   name=\"thumbnail_image\"
                                   accept=\"image/*\"
                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                            <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px. Leave empty to keep current thumbnail.
                            </small>

                            <!-- Current Thumbnail Display -->
                            {% if course.thumbnailImage %}
                            <div class=\"current-thumbnail mt-3 d-flex flex-column align-items-center\">
                                <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                    <img src=\"{{ course.thumbnailUrl }}\" alt=\"Current Thumbnail\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                </div>
                                <small class=\"form-text text-info d-block mt-2 text-center\">Current Thumbnail (300x200px)</small>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Course has modules toggle -->
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-puzzle-piece text-primary mr-1\" aria-hidden=\"true\"></i>
                                Course Structure
                            </label>
                            <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-top: 0.5rem;\">
                                <input class=\"form-check-input\" type=\"checkbox\" id=\"has_modules\" name=\"has_modules\" value=\"1\" {{ course.hasModules ? 'checked' : '' }} style=\"margin-left: 0;\">
                                <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem;\">
                                    Course has modules
                                </label>
                            </div>
                            <small class=\"form-text text-muted\">Enable this if you want to add modules to this course.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class=\"card-footer\" style=\"background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;\">
                            <i class=\"fas fa-save mr-2\"></i>
                            Update Onsite Course
                        </button>
                    </div>
                    <div class=\"col-md-6 text-right\">
                        <a href=\"{{ path('admin_onsite_courses') }}\" class=\"btn btn-secondary btn-lg\" style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;\">
                            <i class=\"fas fa-times mr-2\"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove learning outcome
    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Live instructor sessions, Downloadable resources...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove feature
    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Thumbnail image preview
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Update Onsite Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Toggle module section visibility (if modules section exists)
    \$('#has_modules').on('change', function() {
        const modulesSection = \$('#modules-section');
        if (modulesSection.length) {
            if (\$(this).is(':checked')) {
                modulesSection.slideDown();
            } else {
                modulesSection.slideUp();
            }
        }
    });

    // Thumbnail preview functionality
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const currentThumbnailContainer = document.querySelector('.current-thumbnail');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update the current thumbnail display with new file
                    if (currentThumbnailContainer) {
                        const imgElement = currentThumbnailContainer.querySelector('img');
                        if (imgElement) {
                            imgElement.src = e.target.result;
                        }

                        // Update the label to show it's a new thumbnail
                        const label = currentThumbnailContainer.querySelector('small');
                        if (label) {
                            label.innerHTML = `New Thumbnail Selected: \${file.name}`;
                            label.className = 'form-text text-success d-block mt-2 text-center';
                        }
                    } else {
                        // Create new thumbnail preview if no current thumbnail exists
                        const thumbnailContainer = document.createElement('div');
                        thumbnailContainer.className = 'current-thumbnail mt-3 d-flex flex-column align-items-center';
                        thumbnailContainer.innerHTML = `
                            <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                <img src=\"\${e.target.result}\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                            </div>
                            <small class=\"form-text text-success d-block mt-2 text-center\">New Thumbnail Selected: \${file.name}</small>
                        `;
                        thumbnailInput.parentNode.appendChild(thumbnailContainer);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        \$(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        \$(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<style>
/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 1rem !important;
    padding-top: 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1rem !important;
}

.enhanced-dropdown option:first-child {
    color: #6c757d;
    font-style: italic;
    font-weight: 400;
}

.enhanced-dropdown option:not(:first-child) {
    color: #011a2d;
    font-weight: 500;
}
</style>
{% endblock %}
", "admin/onsite_courses/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\edit.html.twig");
    }
}
