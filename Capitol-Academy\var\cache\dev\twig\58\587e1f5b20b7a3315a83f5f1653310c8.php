<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/preview.html.twig */
class __TwigTemplate_1d26feb97aa7245c285189a0c3404e5e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/preview.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/preview.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Onsite Course Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Onsite Course Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Onsite Course Details: ";
        // line 24
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 24, $this->source); })()), "title", [], "any", false, false, false, 24), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Onsite Course Button (Icon Only) -->
                        <a href=\"";
        // line 30
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 30, $this->source); })()), "code", [], "any", false, false, false, 30)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Onsite Course\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Onsite Course Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Onsite Course Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 50
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Course Code and Title (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-4\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                    Course Code
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 75
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 75, $this->source); })()), "code", [], "any", false, false, false, 75), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-8\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Course Title
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 86
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 86, $this->source); })()), "title", [], "any", false, false, false, 86), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left text-primary mr-1\"></i>
                            Description
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;\">
                            ";
        // line 99
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "description", [], "any", true, true, false, 99) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 99, $this->source); })()), "description", [], "any", false, false, false, 99)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 99, $this->source); })()), "description", [], "any", false, false, false, 99), "html", null, true)) : ("No description provided"));
        yield "
                        </div>
                    </div>

                    <!-- Category and Level (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-folder text-primary mr-1\"></i>
                                    Category
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 112
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "category", [], "any", true, true, false, 112) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 112, $this->source); })()), "category", [], "any", false, false, false, 112)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 112, $this->source); })()), "category", [], "any", false, false, false, 112), "html", null, true)) : ("Uncategorized"));
        yield "
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                    Level
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 123
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "level", [], "any", true, true, false, 123) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 123, $this->source); })()), "level", [], "any", false, false, false, 123)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 123, $this->source); })()), "level", [], "any", false, false, false, 123), "html", null, true)) : ("Not specified"));
        yield "
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Outcomes -->
                    ";
        // line 130
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 130, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 130) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 130, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 130)) > 0))) {
            // line 131
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-bullseye text-primary mr-1\"></i>
                            Learning Outcomes
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                ";
            // line 138
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 138, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 138));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 139
                yield "                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-check-circle text-success me-2\"></i>
                                        ";
                // line 141
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "
                                    </li>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 144
            yield "                            </ul>
                        </div>
                    </div>
                    ";
        }
        // line 148
        yield "
                    <!-- Features -->
                    ";
        // line 150
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 150, $this->source); })()), "features", [], "any", false, false, false, 150) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 150, $this->source); })()), "features", [], "any", false, false, false, 150)) > 0))) {
            // line 151
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-star text-primary mr-1\"></i>
                            Features
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                ";
            // line 158
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 158, $this->source); })()), "features", [], "any", false, false, false, 158));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 159
                yield "                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-star text-warning me-2\"></i>
                                        ";
                // line 161
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "
                                    </li>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 164
            yield "                            </ul>
                        </div>
                    </div>
                    ";
        }
        // line 168
        yield "
                    <!-- Thumbnail -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-image text-primary mr-1\"></i>
                            Course Thumbnail
                        </label>
                        <div class=\"d-flex justify-content-center\">
                            ";
        // line 176
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 176, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 176)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 177
            yield "                                <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 177, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 177), "html", null, true);
            yield "\"
                                     alt=\"Course Thumbnail\"
                                     class=\"img-fluid\"
                                     style=\"width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                            ";
        } else {
            // line 182
            yield "                                <div class=\"d-flex align-items-center justify-content-center\"
                                     style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                    <div class=\"text-center text-muted\">
                                        <i class=\"fas fa-image\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                        <p>No thumbnail available</p>
                                    </div>
                                </div>
                            ";
        }
        // line 190
        yield "                        </div>
                    </div>

                    <!-- Modules Information -->
                    ";
        // line 194
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 194, $this->source); })()), "hasModules", [], "any", false, false, false, 194)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 195
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-puzzle-piece text-primary mr-1\"></i>
                            Course Modules
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            ";
            // line 201
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 201, $this->source); })()), "modules", [], "any", false, false, false, 201) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 201, $this->source); })()), "modules", [], "any", false, false, false, 201)) > 0))) {
                // line 202
                yield "                                <div class=\"row\">
                                    ";
                // line 203
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 203, $this->source); })()), "modules", [], "any", false, false, false, 203));
                $context['loop'] = [
                  'parent' => $context['_parent'],
                  'index0' => 0,
                  'index'  => 1,
                  'first'  => true,
                ];
                if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                    $length = count($context['_seq']);
                    $context['loop']['revindex0'] = $length - 1;
                    $context['loop']['revindex'] = $length;
                    $context['loop']['length'] = $length;
                    $context['loop']['last'] = 1 === $length;
                }
                foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                    // line 204
                    yield "                                    <div class=\"col-12 mb-4\">
                                        <div class=\"module-card\" style=\"background: white; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                            <!-- Module Header -->
                                            <div class=\"module-header mb-3\" style=\"border-bottom: 2px solid #f8f9fa; padding-bottom: 1rem;\">
                                                <h5 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 1.2rem;\">
                                                    <i class=\"fas fa-cube me-2\" style=\"color: #007bff;\"></i>";
                    // line 209
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 209), "html", null, true);
                    yield "
                                                    <span class=\"badge bg-light text-dark ms-2\" style=\"font-size: 0.7rem;\">Module ";
                    // line 210
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 210), "html", null, true);
                    yield "</span>
                                                </h5>
                                                ";
                    // line 212
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 212)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        // line 213
                        yield "                                                    <p class=\"mb-0 text-muted\" style=\"font-size: 0.95rem; line-height: 1.5;\">";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 213), "html", null, true);
                        yield "</p>
                                                ";
                    }
                    // line 215
                    yield "                                            </div>

                                            <!-- Module Content -->
                                            <div class=\"row\">
                                                <!-- Learning Outcomes -->
                                                ";
                    // line 220
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 220) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 220)) > 0))) {
                        // line 221
                        yield "                                                <div class=\"col-md-6 mb-3\">
                                                    <h6 class=\"mb-3\" style=\"color: #011a2d; font-weight: 600;\">
                                                        <i class=\"fas fa-bullseye text-success me-2\"></i>Learning Outcomes
                                                    </h6>
                                                    <ul class=\"mb-0\" style=\"padding-left: 1rem; list-style: none;\">
                                                        ";
                        // line 226
                        $context['_parent'] = $context;
                        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 226));
                        foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                            // line 227
                            yield "                                                            <li class=\"mb-2\" style=\"font-size: 0.9rem; line-height: 1.4;\">
                                                                <i class=\"fas fa-check-circle text-success me-2\" style=\"font-size: 0.8rem;\"></i>
                                                                ";
                            // line 229
                            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                            yield "
                                                            </li>
                                                        ";
                        }
                        $_parent = $context['_parent'];
                        unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
                        $context = array_intersect_key($context, $_parent) + $_parent;
                        // line 232
                        yield "                                                    </ul>
                                                </div>
                                                ";
                    }
                    // line 235
                    yield "
                                                <!-- Features -->
                                                ";
                    // line 237
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 237) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 237)) > 0))) {
                        // line 238
                        yield "                                                <div class=\"col-md-6 mb-3\">
                                                    <h6 class=\"mb-3\" style=\"color: #011a2d; font-weight: 600;\">
                                                        <i class=\"fas fa-star text-warning me-2\"></i>Features
                                                    </h6>
                                                    <ul class=\"mb-0\" style=\"padding-left: 1rem; list-style: none;\">
                                                        ";
                        // line 243
                        $context['_parent'] = $context;
                        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 243));
                        foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                            // line 244
                            yield "                                                            <li class=\"mb-2\" style=\"font-size: 0.9rem; line-height: 1.4;\">
                                                                <i class=\"fas fa-star text-warning me-2\" style=\"font-size: 0.8rem;\"></i>
                                                                ";
                            // line 246
                            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                            yield "
                                                            </li>
                                                        ";
                        }
                        $_parent = $context['_parent'];
                        unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
                        $context = array_intersect_key($context, $_parent) + $_parent;
                        // line 249
                        yield "                                                    </ul>
                                                </div>
                                                ";
                    }
                    // line 252
                    yield "
                                                <!-- If no outcomes or features -->
                                                ";
                    // line 254
                    if ((( !CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 254) || (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 254)) == 0)) && ( !CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 254) || (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 254)) == 0)))) {
                        // line 255
                        yield "                                                <div class=\"col-12\">
                                                    <p class=\"mb-0 text-muted text-center\" style=\"font-style: italic;\">
                                                        <i class=\"fas fa-info-circle me-2\"></i>No learning outcomes or features defined for this module yet.
                                                    </p>
                                                </div>
                                                ";
                    }
                    // line 261
                    yield "                                            </div>
                                        </div>
                                    </div>
                                    ";
                    ++$context['loop']['index0'];
                    ++$context['loop']['index'];
                    $context['loop']['first'] = false;
                    if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                        --$context['loop']['revindex0'];
                        --$context['loop']['revindex'];
                        $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                    }
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 265
                yield "                                </div>
                            ";
            } else {
                // line 267
                yield "                                <p class=\"mb-0 text-muted\">
                                    <i class=\"fas fa-info-circle me-2\"></i>This course has modules enabled but no modules have been created yet.
                                </p>
                            ";
            }
            // line 271
            yield "                        </div>
                    </div>
                    ";
        }
        // line 274
        yield "
                    <!-- Status and Creation Date -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                    Status
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    ";
        // line 284
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 284, $this->source); })()), "isActive", [], "any", false, false, false, 284)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 285
            yield "                                        <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-check-circle mr-1\"></i>Active
                                        </span>
                                    ";
        } else {
            // line 289
            yield "                                        <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                        </span>
                                    ";
        }
        // line 293
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                    Creation Date
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    ";
        // line 303
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 303, $this->source); })()), "createdAt", [], "any", false, false, false, 303), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/preview.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  624 => 303,  612 => 293,  606 => 289,  600 => 285,  598 => 284,  586 => 274,  581 => 271,  575 => 267,  571 => 265,  554 => 261,  546 => 255,  544 => 254,  540 => 252,  535 => 249,  526 => 246,  522 => 244,  518 => 243,  511 => 238,  509 => 237,  505 => 235,  500 => 232,  491 => 229,  487 => 227,  483 => 226,  476 => 221,  474 => 220,  467 => 215,  461 => 213,  459 => 212,  454 => 210,  450 => 209,  443 => 204,  426 => 203,  423 => 202,  421 => 201,  413 => 195,  411 => 194,  405 => 190,  395 => 182,  386 => 177,  384 => 176,  374 => 168,  368 => 164,  359 => 161,  355 => 159,  351 => 158,  342 => 151,  340 => 150,  336 => 148,  330 => 144,  321 => 141,  317 => 139,  313 => 138,  304 => 131,  302 => 130,  292 => 123,  278 => 112,  262 => 99,  246 => 86,  232 => 75,  204 => 50,  181 => 30,  172 => 24,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Course Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Onsite Course Details: {{ course.title }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Onsite Course Button (Icon Only) -->
                        <a href=\"{{ path('admin_onsite_course_edit', {'code': course.code}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Onsite Course\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Onsite Course Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Onsite Course Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Course Code and Title (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-4\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                    Course Code
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.code }}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-8\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Course Title
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.title }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left text-primary mr-1\"></i>
                            Description
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;\">
                            {{ course.description ?? 'No description provided' }}
                        </div>
                    </div>

                    <!-- Category and Level (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-folder text-primary mr-1\"></i>
                                    Category
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.category ?? 'Uncategorized' }}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                    Level
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.level ?? 'Not specified' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Outcomes -->
                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-bullseye text-primary mr-1\"></i>
                            Learning Outcomes
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                {% for outcome in course.learningOutcomes %}
                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-check-circle text-success me-2\"></i>
                                        {{ outcome }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Features -->
                    {% if course.features and course.features|length > 0 %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-star text-primary mr-1\"></i>
                            Features
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                {% for feature in course.features %}
                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-star text-warning me-2\"></i>
                                        {{ feature }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Thumbnail -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-image text-primary mr-1\"></i>
                            Course Thumbnail
                        </label>
                        <div class=\"d-flex justify-content-center\">
                            {% if course.thumbnailImage %}
                                <img src=\"{{ course.thumbnailUrl }}\"
                                     alt=\"Course Thumbnail\"
                                     class=\"img-fluid\"
                                     style=\"width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                            {% else %}
                                <div class=\"d-flex align-items-center justify-content-center\"
                                     style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                    <div class=\"text-center text-muted\">
                                        <i class=\"fas fa-image\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                        <p>No thumbnail available</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Modules Information -->
                    {% if course.hasModules %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-puzzle-piece text-primary mr-1\"></i>
                            Course Modules
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            {% if course.modules and course.modules|length > 0 %}
                                <div class=\"row\">
                                    {% for module in course.modules %}
                                    <div class=\"col-12 mb-4\">
                                        <div class=\"module-card\" style=\"background: white; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                            <!-- Module Header -->
                                            <div class=\"module-header mb-3\" style=\"border-bottom: 2px solid #f8f9fa; padding-bottom: 1rem;\">
                                                <h5 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 1.2rem;\">
                                                    <i class=\"fas fa-cube me-2\" style=\"color: #007bff;\"></i>{{ module.title }}
                                                    <span class=\"badge bg-light text-dark ms-2\" style=\"font-size: 0.7rem;\">Module {{ loop.index }}</span>
                                                </h5>
                                                {% if module.description %}
                                                    <p class=\"mb-0 text-muted\" style=\"font-size: 0.95rem; line-height: 1.5;\">{{ module.description }}</p>
                                                {% endif %}
                                            </div>

                                            <!-- Module Content -->
                                            <div class=\"row\">
                                                <!-- Learning Outcomes -->
                                                {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                <div class=\"col-md-6 mb-3\">
                                                    <h6 class=\"mb-3\" style=\"color: #011a2d; font-weight: 600;\">
                                                        <i class=\"fas fa-bullseye text-success me-2\"></i>Learning Outcomes
                                                    </h6>
                                                    <ul class=\"mb-0\" style=\"padding-left: 1rem; list-style: none;\">
                                                        {% for outcome in module.learningOutcomes %}
                                                            <li class=\"mb-2\" style=\"font-size: 0.9rem; line-height: 1.4;\">
                                                                <i class=\"fas fa-check-circle text-success me-2\" style=\"font-size: 0.8rem;\"></i>
                                                                {{ outcome }}
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                                {% endif %}

                                                <!-- Features -->
                                                {% if module.features and module.features|length > 0 %}
                                                <div class=\"col-md-6 mb-3\">
                                                    <h6 class=\"mb-3\" style=\"color: #011a2d; font-weight: 600;\">
                                                        <i class=\"fas fa-star text-warning me-2\"></i>Features
                                                    </h6>
                                                    <ul class=\"mb-0\" style=\"padding-left: 1rem; list-style: none;\">
                                                        {% for feature in module.features %}
                                                            <li class=\"mb-2\" style=\"font-size: 0.9rem; line-height: 1.4;\">
                                                                <i class=\"fas fa-star text-warning me-2\" style=\"font-size: 0.8rem;\"></i>
                                                                {{ feature }}
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                                {% endif %}

                                                <!-- If no outcomes or features -->
                                                {% if (not module.learningOutcomes or module.learningOutcomes|length == 0) and (not module.features or module.features|length == 0) %}
                                                <div class=\"col-12\">
                                                    <p class=\"mb-0 text-muted text-center\" style=\"font-style: italic;\">
                                                        <i class=\"fas fa-info-circle me-2\"></i>No learning outcomes or features defined for this module yet.
                                                    </p>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class=\"mb-0 text-muted\">
                                    <i class=\"fas fa-info-circle me-2\"></i>This course has modules enabled but no modules have been created yet.
                                </p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Status and Creation Date -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                    Status
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    {% if course.isActive %}
                                        <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-check-circle mr-1\"></i>Active
                                        </span>
                                    {% else %}
                                        <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                    Creation Date
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    {{ course.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
", "admin/onsite_courses/preview.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\preview.html.twig");
    }
}
